import { useEffect, useState } from 'react';
import ModelScene from './ModelScene';

interface GlobalModelProps {
  activeSection: string;
}

const GlobalModel: React.FC<GlobalModelProps> = ({ activeSection }) => {
  const [isVisible, setIsVisible] = useState(true);

  // Hide model during loading or on mobile for performance
  useEffect(() => {
    const checkVisibility = () => {
      const isMobile = window.innerWidth < 768;
      setIsVisible(!isMobile);
    };

    checkVisibility();
    window.addEventListener('resize', checkVisibility);
    
    return () => window.removeEventListener('resize', checkVisibility);
  }, []);

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 pointer-events-none z-10">
      {/* Global 3D Model Container */}
      <div className="absolute right-0 top-0 w-1/2 h-full lg:w-2/5 xl:w-1/3">
        <ModelScene 
          currentSection={activeSection}
          className="opacity-90"
        />
      </div>
      
      {/* Optional: Add a subtle gradient overlay for better text readability */}
      <div className="absolute right-0 top-0 w-1/2 h-full lg:w-2/5 xl:w-1/3 bg-gradient-to-l from-transparent via-transparent to-[#050816]/20 pointer-events-none" />
    </div>
  );
};

export default GlobalModel;
