import { useEffect, useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface CursorProps {
  enabled: boolean;
}

const Cursor: React.FC<CursorProps> = ({ enabled }) => {
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [clicked, setClicked] = useState(false);
  const [linkHovered, setLinkHovered] = useState(false);
  const [hidden, setHidden] = useState(false);
  const [ripples, setRipples] = useState<{ id: number; x: number; y: number }[]>([]);
  const cursorRef = useRef<HTMLDivElement>(null);
  const cursorDotRef = useRef<HTMLDivElement>(null);
  const rippleIdRef = useRef(0);
  
  // If reduced motion is enabled, don't show custom cursor
  if (!enabled) return null;
  
  useEffect(() => {
    // Add event listeners for cursor movement
    const addEventListeners = () => {
      document.addEventListener('mousemove', onMouseMove);
      document.addEventListener('mouseenter', onMouseEnter);
      document.addEventListener('mouseleave', onMouseLeave);
      document.addEventListener('mousedown', onMouseDown);
      document.addEventListener('mouseup', onMouseUp);
    };

    // Remove event listeners
    const removeEventListeners = () => {
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseenter', onMouseEnter);
      document.removeEventListener('mouseleave', onMouseLeave);
      document.removeEventListener('mousedown', onMouseDown);
      document.removeEventListener('mouseup', onMouseUp);
    };

    // Handle cursor movement
    const onMouseMove = (e: MouseEvent) => {
      setPosition({ x: e.clientX, y: e.clientY });
    };

    // Handle cursor entering window
    const onMouseEnter = () => {
      setHidden(false);
    };

    // Handle cursor leaving window
    const onMouseLeave = () => {
      setHidden(true);
    };

    // Handle mouse down
    const onMouseDown = (e: MouseEvent) => {
      setClicked(true);
      
      // Create ripple effect
      const newRipple = {
        id: rippleIdRef.current,
        x: e.clientX,
        y: e.clientY
      };
      
      rippleIdRef.current += 1;
      setRipples(prev => [...prev, newRipple]);
      
      // Remove ripple after animation completes
      setTimeout(() => {
        setRipples(prev => prev.filter(ripple => ripple.id !== newRipple.id));
      }, 1000);
    };

    // Handle mouse up
    const onMouseUp = () => {
      setClicked(false);
    };

    // Add event listeners
    addEventListeners();

    // Handle hover effects for interactive elements
    const handleLinkHoverEvents = () => {
      document.querySelectorAll('a, button, [role="button"], input, textarea, select, [data-magnetic]').forEach(el => {
        el.addEventListener('mouseenter', () => setLinkHovered(true));
        el.addEventListener('mouseleave', () => setLinkHovered(false));
      });
    };

    // Add magnetic effect to elements with data-magnetic attribute
    const addMagneticEffect = () => {
      const magneticElements = document.querySelectorAll('[data-magnetic]');
      
      magneticElements.forEach((el: any) => {
        el.addEventListener('mousemove', (e: MouseEvent) => {
          const rect = (el as HTMLElement).getBoundingClientRect();
          const centerX = rect.left + rect.width / 2;
          const centerY = rect.top + rect.height / 2;
          
          // Calculate distance from cursor to center of element
          const distanceX = e.clientX - centerX;
          const distanceY = e.clientY - centerY;
          
          // Apply enhanced magnetic effect (pull element slightly toward cursor)
      const strength = 15; // Increased strength for more noticeable effect
      const maxDistance = Math.sqrt(rect.width * rect.width + rect.height * rect.height) / 2;
      const distance = Math.sqrt(distanceX * distanceX + distanceY * distanceY);
      const factor = Math.min(1, distance / maxDistance);
      
      (el as HTMLElement).style.transform = `translate(${distanceX / strength}px, ${distanceY / strength}px) scale(${1 + (1 - factor) * 0.1})`;
        });
        
        el.addEventListener('mouseleave', () => {
          // Reset position when cursor leaves
          (el as HTMLElement).style.transform = '';
        });
      });
    };

    // Initialize hover and magnetic effects
    handleLinkHoverEvents();
    addMagneticEffect();

    // Cleanup event listeners on component unmount
    return () => {
      removeEventListeners();
    };
  }, []);

  // Cursor animation variants
  const variants = {
    default: {
      x: position.x - 16,
      y: position.y - 16,
      opacity: hidden ? 0 : 1,
    },
    dot: {
      x: position.x - 4,
      y: position.y - 4,
      opacity: hidden ? 0 : 1,
    }
  };

  return (
    <>
      {/* Main cursor ring */}
      <motion.div
        ref={cursorRef}
        className="cursor-ring fixed top-0 left-0 w-8 h-8 rounded-full pointer-events-none z-50 border-2"
        animate={{
          ...variants.default,
          scale: clicked ? 0.8 : linkHovered ? 1.5 : 1,
          borderColor: linkHovered ? '#00ff9e' : 'rgb(255, 255, 255)',
          backgroundColor: linkHovered ? 'rgba(0, 255, 158, 0.1)' : 'transparent',
        }}
        transition={{
          type: 'spring',
          stiffness: 150,
          damping: 15,
          mass: 0.1
        }}
      />
      
      {/* Cursor dot */}
      <motion.div
        ref={cursorDotRef}
        className="cursor-dot fixed top-0 left-0 w-2 h-2 rounded-full pointer-events-none z-50"
        animate={{
          ...variants.dot,
          backgroundColor: linkHovered ? '#00ff9e' : 'rgb(255, 255, 255)',
        }}
        transition={{
          type: 'spring',
          stiffness: 250,
          damping: 10,
          mass: 0.05
        }}
      />
      
      {/* Click ripples */}
      <AnimatePresence>
        {ripples.map(ripple => (
          <motion.div
            key={ripple.id}
            initial={{ width: 0, height: 0, x: ripple.x, y: ripple.y, opacity: 0.5 }}
            animate={{ width: 100, height: 100, x: ripple.x - 50, y: ripple.y - 50, opacity: 0 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 1, ease: 'easeOut' }}
            className="fixed rounded-full pointer-events-none z-40 border border-[#00ff9e]"
            style={{ translateX: '-50%', translateY: '-50%' }}
          />
        ))}
      </AnimatePresence>
      
      {/* Hide default cursor */}
      <style>{`
        body {
          cursor: none;
        }
        a, button, [role="button"], input, textarea, select, [data-magnetic] {
          cursor: none;
        }
      `}</style>
    </>
  );
};

export default Cursor;