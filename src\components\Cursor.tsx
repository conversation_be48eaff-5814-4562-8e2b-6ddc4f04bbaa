import { useEffect, useState, useRef, useCallback } from 'react';
import { motion, AnimatePresence, useMotionValue, useSpring } from 'framer-motion';

interface CursorProps {
  enabled: boolean;
}

const Cursor: React.FC<CursorProps> = ({ enabled }) => {
  const [clicked, setClicked] = useState(false);
  const [linkHovered, setLinkHovered] = useState(false);
  const [hidden, setHidden] = useState(false);
  const [ripples, setRipples] = useState<{ id: number; x: number; y: number }[]>([]);
  const cursorRef = useRef<HTMLDivElement>(null);
  const cursorDotRef = useRef<HTMLDivElement>(null);
  const rippleIdRef = useRef(0);
  const rafId = useRef<number>();

  // Use motion values for better performance
  const cursorX = useMotionValue(0);
  const cursorY = useMotionValue(0);
  const dotX = useMotionValue(0);
  const dotY = useMotionValue(0);

  // Use springs for smooth movement with optimized settings
  const springConfig = { damping: 30, stiffness: 800, mass: 0.2 };
  const cursorXSpring = useSpring(cursorX, springConfig);
  const cursorYSpring = useSpring(cursorY, springConfig);
  const dotXSpring = useSpring(dotX, { damping: 20, stiffness: 1200, mass: 0.1 });
  const dotYSpring = useSpring(dotY, { damping: 20, stiffness: 1200, mass: 0.1 });
  
  // If reduced motion is enabled, don't show custom cursor
  if (!enabled) return null;
  
  useEffect(() => {
    // Add event listeners for cursor movement
    const addEventListeners = () => {
      document.addEventListener('mousemove', onMouseMove);
      document.addEventListener('mouseenter', onMouseEnter);
      document.addEventListener('mouseleave', onMouseLeave);
      document.addEventListener('mousedown', onMouseDown);
      document.addEventListener('mouseup', onMouseUp);
    };

    // Remove event listeners
    const removeEventListeners = () => {
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseenter', onMouseEnter);
      document.removeEventListener('mouseleave', onMouseLeave);
      document.removeEventListener('mousedown', onMouseDown);
      document.removeEventListener('mouseup', onMouseUp);
    };

    // Handle cursor movement with RAF throttling for better performance
    const onMouseMove = useCallback((e: MouseEvent) => {
      // Cancel previous frame if still pending
      if (rafId.current) {
        cancelAnimationFrame(rafId.current);
      }

      // Use requestAnimationFrame for smooth updates
      rafId.current = requestAnimationFrame(() => {
        cursorX.set(e.clientX - 16);
        cursorY.set(e.clientY - 16);
        dotX.set(e.clientX - 4);
        dotY.set(e.clientY - 4);
      });
    }, [cursorX, cursorY, dotX, dotY]);

    // Handle cursor entering window
    const onMouseEnter = () => {
      setHidden(false);
    };

    // Handle cursor leaving window
    const onMouseLeave = () => {
      setHidden(true);
    };

    // Handle mouse down
    const onMouseDown = (e: MouseEvent) => {
      setClicked(true);
      
      // Create ripple effect
      const newRipple = {
        id: rippleIdRef.current,
        x: e.clientX,
        y: e.clientY
      };
      
      rippleIdRef.current += 1;
      setRipples(prev => [...prev, newRipple]);
      
      // Remove ripple after animation completes
      setTimeout(() => {
        setRipples(prev => prev.filter(ripple => ripple.id !== newRipple.id));
      }, 1000);
    };

    // Handle mouse up
    const onMouseUp = () => {
      setClicked(false);
    };

    // Add event listeners
    addEventListeners();

    // Handle hover effects for interactive elements
    const handleLinkHoverEvents = () => {
      document.querySelectorAll('a, button, [role="button"], input, textarea, select, [data-magnetic]').forEach(el => {
        el.addEventListener('mouseenter', () => setLinkHovered(true));
        el.addEventListener('mouseleave', () => setLinkHovered(false));
      });
    };

    // Add optimized magnetic effect to elements with data-magnetic attribute
    const addMagneticEffect = () => {
      const magneticElements = document.querySelectorAll('[data-magnetic]');

      magneticElements.forEach((el: any) => {
        let animationId: number;

        const handleMouseMove = (e: MouseEvent) => {
          // Cancel previous animation frame
          if (animationId) {
            cancelAnimationFrame(animationId);
          }

          animationId = requestAnimationFrame(() => {
            const rect = (el as HTMLElement).getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;

            // Calculate distance from cursor to center of element
            const distanceX = e.clientX - centerX;
            const distanceY = e.clientY - centerY;

            // Apply optimized magnetic effect
            const strength = 20;
            const translateX = distanceX / strength;
            const translateY = distanceY / strength;

            (el as HTMLElement).style.transform = `translate3d(${translateX}px, ${translateY}px, 0) scale(1.05)`;
          });
        };

        el.addEventListener('mousemove', handleMouseMove);

        el.addEventListener('mouseleave', () => {
          // Cancel any pending animation
          if (animationId) {
            cancelAnimationFrame(animationId);
          }
          // Reset position when cursor leaves
          (el as HTMLElement).style.transform = '';
        });
      });
    };

    // Initialize hover and magnetic effects
    handleLinkHoverEvents();
    addMagneticEffect();

    // Cleanup event listeners on component unmount
    return () => {
      removeEventListeners();
    };
  }, []);

  return (
    <>
      {/* Main cursor ring */}
      <motion.div
        ref={cursorRef}
        className="cursor-ring fixed top-0 left-0 w-8 h-8 rounded-full pointer-events-none z-50 border-2"
        style={{
          x: cursorXSpring,
          y: cursorYSpring,
        }}
        animate={{
          scale: clicked ? 0.8 : linkHovered ? 1.5 : 1,
          borderColor: linkHovered ? '#00ff9e' : 'rgb(255, 255, 255)',
          backgroundColor: linkHovered ? 'rgba(0, 255, 158, 0.1)' : 'transparent',
          opacity: hidden ? 0 : 1,
        }}
        transition={{
          scale: { type: 'spring', stiffness: 300, damping: 20 },
          borderColor: { duration: 0.2 },
          backgroundColor: { duration: 0.2 },
          opacity: { duration: 0.1 }
        }}
      />

      {/* Cursor dot */}
      <motion.div
        ref={cursorDotRef}
        className="cursor-dot fixed top-0 left-0 w-2 h-2 rounded-full pointer-events-none z-50"
        style={{
          x: dotXSpring,
          y: dotYSpring,
        }}
        animate={{
          backgroundColor: linkHovered ? '#00ff9e' : 'rgb(255, 255, 255)',
          opacity: hidden ? 0 : 1,
        }}
        transition={{
          backgroundColor: { duration: 0.2 },
          opacity: { duration: 0.1 }
        }}
      />
      
      {/* Click ripples */}
      <AnimatePresence>
        {ripples.map(ripple => (
          <motion.div
            key={ripple.id}
            initial={{ width: 0, height: 0, x: ripple.x, y: ripple.y, opacity: 0.5 }}
            animate={{ width: 100, height: 100, x: ripple.x - 50, y: ripple.y - 50, opacity: 0 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 1, ease: 'easeOut' }}
            className="fixed rounded-full pointer-events-none z-40 border border-[#00ff9e]"
            style={{ translateX: '-50%', translateY: '-50%' }}
          />
        ))}
      </AnimatePresence>
      
      {/* Hide default cursor */}
      <style>{`
        body {
          cursor: none;
        }
        a, button, [role="button"], input, textarea, select, [data-magnetic] {
          cursor: none;
        }
      `}</style>
    </>
  );
};

export default Cursor;