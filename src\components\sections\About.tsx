import { useEffect, useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import info from '@/assets/info.json';

interface AboutProps {
  appContext: {
    soundEnabled: boolean;
    toggleSound: () => void;
    reducedMotion: boolean;
    toggleReducedMotion: () => void;
    playSoundEffect: (type: 'hover' | 'click' | 'transition') => void;
  };
}

const About: React.FC<AboutProps> = ({ appContext }) => {
  const { reducedMotion } = appContext;
  const bioRef = useRef<HTMLDivElement>(null);
  const inView = useInView(bioRef, { once: true, amount: 0.3 });
  
  // Split bio into paragraphs
  const bioParagraphs = info.personal.summary;
  
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };
  
  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15
      }
    }
  };
  
  return (
    <section id="about" className="min-h-screen pt-24 pb-16 px-4 relative overflow-hidden">
      <div className="container mx-auto max-w-5xl relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="mb-12 text-center"
        >
          <h2 className="text-4xl md:text-5xl font-bold font-space-grotesk mb-4">
            About <span className="text-[#00ff9e]">Me</span>
          </h2>
          <div className="w-24 h-1 bg-[#00ff9e] mx-auto rounded-full" />
        </motion.div>
        
        <div className="grid grid-cols-1 md:grid-cols-12 gap-8 items-center">
          {/* Profile image */}
          <motion.div 
            className="md:col-span-5 flex justify-center"
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <div className="relative">
              <div className="w-64 h-64 md:w-80 md:h-80 rounded-full overflow-hidden border-4 border-[#00ff9e] p-2">
                <div className="w-full h-full rounded-full overflow-hidden bg-[#0a0f1d] flex items-center justify-center">
                  {/* Placeholder for profile image */}
                  <span className="text-6xl font-bold text-[#00ff9e]">{info.personal.name.charAt(0)}</span>
                </div>
              </div>
              
              {/* Decorative elements */}
              <div className="absolute -top-4 -right-4 w-24 h-24 border-t-4 border-r-4 border-[#00ff9e] rounded-tr-3xl" />
              <div className="absolute -bottom-4 -left-4 w-24 h-24 border-b-4 border-l-4 border-[#00ff9e] rounded-bl-3xl" />
            </div>
          </motion.div>
          
          {/* Bio content */}
          <motion.div 
            ref={bioRef}
            className="md:col-span-7"
            variants={reducedMotion ? {} : containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            <motion.h3 
              className="text-2xl md:text-3xl font-bold mb-4 font-sora text-[#00ff9e]"
              variants={reducedMotion ? {} : itemVariants}
            >
              {info.personal.name}
            </motion.h3>
            
            <motion.h4 
              className="text-xl text-gray-300 mb-6 font-inter"
              variants={reducedMotion ? {} : itemVariants}
            >
              {info.personal.title}
            </motion.h4>
            
            <div className="space-y-4">
              {bioParagraphs.map((paragraph, index) => (
                <motion.p 
                  key={index} 
                  className="text-gray-400 leading-relaxed"
                  variants={reducedMotion ? {} : itemVariants}
                >
                  {paragraph}
                </motion.p>
              ))}
            </div>
            
            {/* Contact info */}
            <motion.div 
              className="mt-8 grid grid-cols-1 sm:grid-cols-2 gap-4"
              variants={reducedMotion ? {} : itemVariants}
            >
              {/* Email */}
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-[#0a0f1d] flex items-center justify-center mr-3">
                  <span className="text-[#00ff9e]">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                      <polyline points="22,6 12,13 2,6"></polyline>
                    </svg>
                  </span>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Email</p>
                  <p className="text-white">{info.personal.email}</p>
                </div>
              </div>
              {/* Phone */}
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-[#0a0f1d] flex items-center justify-center mr-3">
                  <span className="text-[#00ff9e]">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                    </svg>
                  </span>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Phone</p>
                  <p className="text-white">{info.personal.phone}</p>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default About;