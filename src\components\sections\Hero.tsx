import { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import * as THREE from 'three';
import info from '@/assets/info.json';

interface HeroProps {
  appContext: {
    soundEnabled: boolean;
    reducedMotion: boolean;
    playSoundEffect: (type: 'hover' | 'click' | 'transition') => void;
  };
}

const Hero: React.FC<HeroProps> = ({ appContext }) => {
  const { reducedMotion } = appContext;
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const nameRef = useRef<HTMLHeadingElement>(null);
  
  // WebGL effect setup
  useEffect(() => {
    if (!canvasRef.current || reducedMotion) return;
    
    const canvas = canvasRef.current;
    const renderer = new THREE.WebGLRenderer({
      canvas,
      alpha: true,
      antialias: true
    });
    
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(
      75,
      window.innerWidth / window.innerHeight,
      0.1,
      1000
    );
    camera.position.z = 5;
    
    // Create animated particles
    const particlesGeometry = new THREE.BufferGeometry();
    const particlesCount = 0 //500;
    const posArray = new Float32Array(particlesCount * 3);
    
    for (let i = 0; i < particlesCount * 3; i++) {
      posArray[i] = (Math.random() - 0.5) * 5;
    }
    
    particlesGeometry.setAttribute('position', new THREE.BufferAttribute(posArray, 3));
    
    const particlesMaterial = new THREE.PointsMaterial({
      size: 0.005,
      color: '#00ff9e',
      blending: THREE.AdditiveBlending,
      transparent: true
    });
    
    const particlesMesh = new THREE.Points(particlesGeometry, particlesMaterial);
    scene.add(particlesMesh);
    
    // Animation
    const animate = () => {
      requestAnimationFrame(animate);
      particlesMesh.rotation.y += 0.001;
      particlesMesh.rotation.x += 0.001;
      renderer.render(scene, camera);
    };
    
    animate();
    
    // Handle resize
    const handleResize = () => {
      camera.aspect = window.innerWidth / window.innerHeight;
      camera.updateProjectionMatrix();
      renderer.setSize(window.innerWidth, window.innerHeight);
    };
    
    window.addEventListener('resize', handleResize);
    
    // Text animation
    // if (nameRef.current) {
    //   gsap.from(nameRef.current, {
    //     y: 100,
    //     opacity: 0,
    //     duration: 1,
    //     ease: 'power4.out',
    //     delay: 0.5
    //   });
    // }
    
    return () => {
      window.removeEventListener('resize', handleResize);
      scene.remove(particlesMesh);
      particlesGeometry.dispose();
      particlesMaterial.dispose();
      renderer.dispose();
    };
  }, [reducedMotion]);
  
  return (
    <section id="hero" className="relative w-full h-screen flex items-center justify-center overflow-hidden">
      {/* WebGL Canvas Background */}
      <canvas
        ref={canvasRef}
        className="absolute inset-0 w-full h-full z-0"
      />
      
      {/* Content */}
      <div className="relative z-10 text-center">
        <motion.h1
          ref={nameRef}
          className="text-5xl md:text-7xl font-bold mb-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: 'easeOut' }}
        >
          <span className="text-[#00ff9e]">{info.personal.name}</span>
        </motion.h1>
        
        <motion.h2
          className="text-2xl md:text-3xl text-gray-300 mb-8"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.3 }}
        >
          {info.personal.title}
        </motion.h2>
        
        <motion.div
          className="max-w-2xl mx-auto px-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <p className="text-lg text-gray-400">
            {info.personal.description}
          </p>
        </motion.div>
      </div>
      
      {/* 3D Model is now global - rendered in App.tsx */}
      
      {/* Scroll Indicator */}
      <motion.div
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ 
          duration: 1,
          delay: 1,
          repeat: Infinity,
          repeatType: 'reverse'
        }}
      >
        <div className="w-6 h-10 border-2 border-[#00ff9e] rounded-full flex items-start justify-center p-2">
          <div className="w-1 h-3 bg-[#00ff9e] rounded-full animate-bounce" />
        </div>
      </motion.div>
    </section>
  );
};

export default Hero;