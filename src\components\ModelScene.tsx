import { useRef, useState, useEffect } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { useFBX, Environment } from '@react-three/drei';
import * as THREE from 'three';

interface RobotModelProps {
  mousePos: { x: number; y: number };
  scrollYProgress: number;
  currentSection: string;
}

function RobotModel({ mousePos, scrollYProgress, currentSection }: RobotModelProps) {
  const groupRef = useRef<THREE.Group>(null);
  const mixerRef = useRef<THREE.AnimationMixer | null>(null);
  const [modelReady, setModelReady] = useState(false);
  const [animationActions, setAnimationActions] = useState<THREE.AnimationAction[]>([]);
  const [bones, setBones] = useState<{ [key: string]: THREE.Bone }>({});

  // Load FBX model with error handling
  let fbx;
  try {
    fbx = useFBX('/models/sci-fi-robot.fbx');
    console.log('FBX loading attempt successful');
  } catch (error) {
    console.error('FBX loading failed:', error);
    fbx = null;
  }

  console.log('FBX Model state:', {
    fbx: !!fbx,
    modelReady,
    animationsCount: fbx?.animations?.length || 0,
    bonesCount: Object.keys(bones).length,
    currentSection,
    scrollYProgress
  });

  // Initialize model when FBX loads
  useEffect(() => {
    if (fbx && groupRef.current) {
      console.log('Setting up FBX Model:', fbx);

      // Clear any existing children
      while (groupRef.current.children.length > 0) {
        groupRef.current.remove(groupRef.current.children[0]);
      }

      // Clone the FBX to avoid modifying the cached version
      const clonedFBX = fbx.clone();

      // Set up the model with MUCH larger scale
      clonedFBX.scale.setScalar(5); // MASSIVE scale - 50x larger than current
      clonedFBX.position.set(3, -5, 0); // Move to the right and down
      clonedFBX.rotation.set(0, Math.PI * 0.25, 0); // Face slightly towards camera

      // Find and store bones for manual animation
      const foundBones: { [key: string]: THREE.Bone } = {};
      clonedFBX.traverse((child: any) => {
        if (child.isBone) {
          foundBones[child.name] = child;
          console.log('Found bone:', child.name);
        }

        if (child.isMesh) {
          child.castShadow = true;
          child.receiveShadow = true;

          // Ensure materials are visible and bright
          if (child.material) {
            if (Array.isArray(child.material)) {
              child.material.forEach((mat: any) => {
                mat.needsUpdate = true;
                if (mat.map) mat.map.needsUpdate = true;
                // Increase material brightness
                if (mat.emissive) mat.emissive.setHex(0x111111);
              });
            } else {
              child.material.needsUpdate = true;
              if (child.material.map) child.material.map.needsUpdate = true;
              // Increase material brightness
              if (child.material.emissive) child.material.emissive.setHex(0x111111);
            }
          }
        }
      });

      setBones(foundBones);
      console.log('Found bones:', Object.keys(foundBones));

      // Add to group
      groupRef.current.add(clonedFBX);

      // Set up animation mixer if animations exist
      if (fbx.animations && fbx.animations.length > 0) {
        console.log('Setting up animations:', fbx.animations.length);
        const mixer = new THREE.AnimationMixer(clonedFBX);
        mixerRef.current = mixer;

        const actions = fbx.animations.map((clip, index) => {
          console.log(`Setting up animation ${index}:`, clip.name, 'Duration:', clip.duration);
          const action = mixer.clipAction(clip);

          // Reset and configure the action properly
          action.reset();
          action.setLoop(THREE.LoopRepeat, Infinity);
          action.clampWhenFinished = false;
          action.enabled = true;
          action.setEffectiveTimeScale(1);
          action.setEffectiveWeight(1);
          action.play();

          console.log('Animation action created and playing:', action.isRunning());
          return action;
        });

        setAnimationActions(actions);
        console.log(`Successfully loaded ${fbx.animations.length} animations`);
      } else {
        console.log('No animations found in FBX - creating manual animations');

        // If no FBX animations, create manual animations
        const mixer = new THREE.AnimationMixer(clonedFBX);
        mixerRef.current = mixer;

        // Create a simple rotation animation
        const times = [0, 1, 2];
        const values = [0, Math.PI, Math.PI * 2];
        const track = new THREE.NumberKeyframeTrack('.rotation[y]', times, values);
        const clip = new THREE.AnimationClip('rotation', 2, [track]);
        const action = mixer.clipAction(clip);
        action.play();
        setAnimationActions([action]);
        console.log('Created manual rotation animation');
      }

      setModelReady(true);
      console.log('Model setup complete - Ready for rendering');
    }
  }, [fbx]);

  // Animation loop - simplified and more effective
  useFrame((state, delta) => {
    if (!groupRef.current || !modelReady) return;

    // CRITICAL: Update animation mixer for FBX animations
    if (mixerRef.current) {
      mixerRef.current.update(delta);
    }

    // Control animation speed based on scroll and section
    if (animationActions.length > 0) {
      animationActions.forEach((action, index) => {
        // Force animations to play if they're not running
        if (!action.isRunning()) {
          console.log(`Restarting animation ${index}`);
          action.reset();
          action.play();
        }

        // Ensure proper weight and time scale
        action.setEffectiveWeight(1);

        // Different animation speeds for different sections
        let speedMultiplier = 1;
        switch (currentSection) {
          case 'hero':
            speedMultiplier = 1.0;
            break;
          case 'about':
            speedMultiplier = 1.5;
            break;
          case 'skills':
            speedMultiplier = 2.0;
            break;
          case 'projects':
            speedMultiplier = 2.5;
            break;
          default:
            speedMultiplier = 1.0;
        }
        action.setEffectiveTimeScale(speedMultiplier);

        // Debug animation state more frequently
        if (Math.floor(time * 2) % 10 === 0) {
          console.log(`Animation ${index} - Running: ${action.isRunning()}, Time: ${action.time.toFixed(2)}, Weight: ${action.getEffectiveWeight()}, Scale: ${action.getEffectiveTimeScale()}`);
        }
      });
    }

    const time = state.clock.getElapsedTime();

    // Mouse-based rotation
    const targetRotationY = Math.PI * 0.25 + (mousePos.x * Math.PI * 0.3);
    const targetRotationX = mousePos.y * Math.PI * 0.1;

    // Apply smooth rotation
    groupRef.current.rotation.y = THREE.MathUtils.lerp(
      groupRef.current.rotation.y,
      targetRotationY,
      0.02
    );

    groupRef.current.rotation.x = THREE.MathUtils.lerp(
      groupRef.current.rotation.x,
      targetRotationX,
      0.02
    );

    // Enhanced floating animation with more movement
    groupRef.current.position.y = -5 + Math.sin(time * 1.2) * 0.8;

    // Add continuous rotation if no animations are working
    if (animationActions.length === 0 || !animationActions.some(action => action.isRunning())) {
      groupRef.current.rotation.y += delta * 0.5; // Slow rotation
      groupRef.current.rotation.z = Math.sin(time * 0.8) * 0.1; // Gentle sway
    }

    // MUCH larger scale
    const baseScale = 5; // MASSIVE scale for high visibility - 50x larger
    const scrollScale = 1 + scrollYProgress * 0.5; // More dramatic scaling
    const finalScale = baseScale * scrollScale;
    groupRef.current.scale.setScalar(finalScale);

    // Debug scale every few seconds
    if (Math.floor(time) % 3 === 0 && Math.floor(time * 10) % 10 === 0) {
      console.log(`Model scale: ${finalScale.toFixed(2)} (base: ${baseScale}, scroll: ${scrollScale.toFixed(2)}, progress: ${scrollYProgress.toFixed(2)})`);
    }

    // Section-based positioning - keep on right side
    const sectionPositions = {
      hero: { x: 3, z: 0 },
      about: { x: 2.5, z: 0.2 },
      skills: { x: 3.5, z: -0.2 },
      experience: { x: 2.8, z: 0.3 },
      projects: { x: 3.2, z: 0.1 },
      contact: { x: 3, z: 0.2 }
    };

    const targetPos = sectionPositions[currentSection as keyof typeof sectionPositions] || { x: 0, z: 0 };
    groupRef.current.position.x = THREE.MathUtils.lerp(
      groupRef.current.position.x,
      targetPos.x,
      0.02
    );
    groupRef.current.position.z = THREE.MathUtils.lerp(
      groupRef.current.position.z,
      targetPos.z,
      0.02
    );

    // Scroll-based rotation
    const scrollRotation = scrollYProgress * Math.PI * 0.5;
    groupRef.current.rotation.z = Math.sin(scrollRotation) * 0.1;

    // Bone-based animations if available
    if (Object.keys(bones).length > 0) {
      // Try common bone names for head tracking
      const headBones = ['Head', 'head', 'mixamorigHead', 'Bip01_Head', 'head_joint'];
      let headBone = null;
      for (const boneName of headBones) {
        if (bones[boneName]) {
          headBone = bones[boneName];
          break;
        }
      }

      if (headBone) {
        headBone.rotation.y = THREE.MathUtils.lerp(
          headBone.rotation.y,
          mousePos.x * 0.5,
          0.03
        );
        headBone.rotation.x = THREE.MathUtils.lerp(
          headBone.rotation.x,
          -mousePos.y * 0.3,
          0.03
        );
      }
    }
  });

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (mixerRef.current) {
        mixerRef.current.stopAllAction();
        mixerRef.current = null;
      }
    };
  }, []);

  // Render animated fallback if model not ready - MASSIVE and always visible
  if (!modelReady) {
    const time = performance.now() * 0.001;
    console.log('Rendering fallback model - FBX not ready');
    return (
      <group
        ref={groupRef}
        scale={15}
        position={[0, -6 + Math.sin(time * 2) * 0.5, 0]}
        rotation={[0, time * 0.8, Math.sin(time) * 0.15]}
      >
        <mesh position={[0, 0, 0]}>
          <boxGeometry args={[1.5, 3, 0.8]} />
          <meshStandardMaterial color="#00ff9e" emissive="#003322" />
        </mesh>
        <mesh position={[0, 1.8, 0]} rotation={[0, time * 2, 0]}>
          <sphereGeometry args={[0.5]} />
          <meshStandardMaterial color="#00ff9e" emissive="#003322" />
        </mesh>
        {/* Animated Arms */}
        <mesh position={[-1.1, 0.8, 0]} rotation={[Math.sin(time * 3) * 0.5, 0, 0]}>
          <boxGeometry args={[0.4, 1.5, 0.4]} />
          <meshStandardMaterial color="#00ff9e" emissive="#003322" />
        </mesh>
        <mesh position={[1.1, 0.8, 0]} rotation={[Math.sin(time * 3 + Math.PI) * 0.5, 0, 0]}>
          <boxGeometry args={[0.4, 1.5, 0.4]} />
          <meshStandardMaterial color="#00ff9e" emissive="#003322" />
        </mesh>
        {/* Legs */}
        <mesh position={[-0.5, -2.2, 0]}>
          <boxGeometry args={[0.45, 2, 0.45]} />
          <meshStandardMaterial color="#00ff9e" emissive="#003322" />
        </mesh>
        <mesh position={[0.5, -2.2, 0]}>
          <boxGeometry args={[0.45, 2, 0.45]} />
          <meshStandardMaterial color="#00ff9e" emissive="#003322" />
        </mesh>
      </group>
    );
  }

  return <group ref={groupRef} />;
}

interface ModelSceneProps {
  currentSection?: string;
  className?: string;
}

export default function ModelScene({ currentSection = 'hero', className = '' }: ModelSceneProps) {
  const [mousePos, setMousePos] = useState({ x: 0, y: 0 });
  const [scrollYProgress, setScrollYProgress] = useState(0);

  // Track scroll position
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollPercent = docHeight > 0 ? scrollTop / docHeight : 0;
      setScrollYProgress(Math.min(Math.max(scrollPercent, 0), 1));
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll();

    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Track mouse position
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePos({
        x: (e.clientX / window.innerWidth) * 2 - 1,
        y: -(e.clientY / window.innerHeight) * 2 + 1,
      });
    };

    window.addEventListener('mousemove', handleMouseMove, { passive: true });

    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return (
    <div className={`w-full h-full ${className}`}>
      <Canvas
        shadows
        dpr={[1, 2]}
        camera={{ position: [8, 6, 10], fov: 60, near: 0.1, far: 1000 }}
        gl={{ antialias: true, alpha: true, powerPreference: "high-performance" }}
      >
        {/* MUCH brighter lighting for better visibility */}
        <ambientLight intensity={1.2} />
        <directionalLight
          position={[10, 15, 10]}
          intensity={3.0}
          castShadow
          shadow-mapSize={[2048, 2048]}
          shadow-camera-far={50}
          shadow-camera-left={-25}
          shadow-camera-right={25}
          shadow-camera-top={25}
          shadow-camera-bottom={-25}
        />
        <pointLight position={[-10, 10, 10]} intensity={2.0} color="#00ff9e" />
        <pointLight position={[10, -10, -10]} intensity={1.5} color="#0066ff" />
        <spotLight
          position={[0, 20, 8]}
          angle={0.4}
          penumbra={1}
          intensity={2.5}
          color="#ffffff"
        />

        <RobotModel
          mousePos={mousePos}
          scrollYProgress={scrollYProgress}
          currentSection={currentSection}
        />

        {/* Debug cubes removed - 3D is working */}
        <Environment preset="sunset" />
      </Canvas>
    </div>
  );
}

// Preload the FBX model
useFBX.preload('/models/sci-fi-robot.fbx');