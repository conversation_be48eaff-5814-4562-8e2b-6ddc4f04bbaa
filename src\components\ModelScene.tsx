import { useRef, useState, useEffect } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { useFBX, Environment } from '@react-three/drei';
import * as THREE from 'three';

function RobotModel({ mousePos, scrollYProgress }: { mousePos: { x: number; y: number }; scrollYProgress: number }) {
  const model = useFBX('/models/sci-fi-robot.fbx');
  const modelRef = useRef<THREE.Group>(null);
  const [modelLoaded, setModelLoaded] = useState(false);

  useEffect(() => {
    if (model && modelRef.current) {
      modelRef.current.scale.setScalar(1.5);
      modelRef.current.position.set(0, -1.5, 0);
      modelRef.current.rotation.set(0, Math.PI * 0.15, 0);
      model.traverse((child: any) => {
        if (child.isMesh) {
          child.castShadow = true;
          child.receiveShadow = true;
          if (child.material) {
            child.material.needsUpdate = true;
          }
        }
      });
      setModelLoaded(true);
    }
  }, [model]);

  useFrame((state) => {
    if (!modelRef.current || !modelLoaded) return;
    const time = state.clock.getElapsedTime();
    // Smooth follow mouse with limits
    const targetRotationY = THREE.MathUtils.lerp(
      -Math.PI / 4,
      Math.PI / 4,
      (mousePos.x + 1) / 2
    );
    const targetRotationX = THREE.MathUtils.lerp(
      -Math.PI / 6,
      Math.PI / 6,
      (mousePos.y + 1) / 2
    );
    modelRef.current.rotation.y = THREE.MathUtils.lerp(
      modelRef.current.rotation.y,
      targetRotationY,
      0.05
    );
    modelRef.current.rotation.x = THREE.MathUtils.lerp(
      modelRef.current.rotation.x,
      targetRotationX,
      0.05
    );
    // Breathing animation
    modelRef.current.position.y = -1.5 + Math.sin(time * 0.5) * 0.1;
    // Scroll-based scale
    const baseScale = 1.5;
    const scrollScale = 1 + scrollYProgress * 0.2;
    modelRef.current.scale.setScalar(baseScale * scrollScale);
  });

  return modelLoaded ? <primitive object={model} ref={modelRef} /> : null;
}

export default function ModelScene() {
  const [mousePos, setMousePos] = useState({ x: 0, y: 0 });
  const scrollY = useRef(0);

  // Track scroll position
  useEffect(() => {
    const handleScroll = () => {
      const maxScroll = document.documentElement.scrollHeight - window.innerHeight;
      scrollY.current = Math.min(window.scrollY / maxScroll, 1);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Track mouse position
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePos({
        x: (e.clientX / window.innerWidth) * 2 - 1,
        y: -(e.clientY / window.innerHeight) * 2 + 1,
      });
    };
    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return (
    <Canvas shadows dpr={[1, 2]} camera={{ position: [0, 0, 5], fov: 45 }}>
      <ambientLight intensity={0.5} />
      <directionalLight
        position={[10, 10, 5]}
        intensity={1}
        castShadow
        shadow-mapSize={[1024, 1024]}
      />
      <pointLight position={[-10, -10, -5]} intensity={0.5} />
      <RobotModel mousePos={mousePos} scrollYProgress={scrollY.current} />
      <Environment preset="city" />
    </Canvas>
  );
}