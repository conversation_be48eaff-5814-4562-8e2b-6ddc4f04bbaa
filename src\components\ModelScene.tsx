import { useRef, useState, useEffect } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { useGLTF, Environment } from '@react-three/drei';
import * as THREE from 'three';
import { useScroll } from 'framer-motion';

interface RobotModelProps {
  scrollYProgress: any;
  mousePosition: { x: number; y: number };
}

// Robot model component
function RobotModel({ scrollYProgress, mousePosition }: RobotModelProps) {
  const { scene, nodes, materials, animations } = useGLTF('/models/sci-fi-robot.glb');

  const modelRef = useRef<THREE.Group>(null);
  const [animationState, setAnimationState] = useState({
    waving: false,
    pointing: false,
    headTracking: true
  });

  // Clone the scene to avoid modifying the cached original
  useEffect(() => {
    if (scene) {
      console.log('Model loaded successfully:', { scene, nodes, materials, animations });

      // Ensure the model is visible by setting proper scale and position
      if (modelRef.current) {
        modelRef.current.scale.setScalar(1);
        modelRef.current.position.set(0, -1, 0);
        modelRef.current.rotation.set(0, 0, 0);
      }
    }
  }, [scene, nodes, materials, animations]);

  // Handle scroll-based animations
  useFrame((state) => {
    if (!modelRef.current) return;

    const scrollProgress = scrollYProgress.get();
    const time = state.clock.elapsedTime;

    // Smooth rotation based on scroll - more dramatic effect
    const targetRotationY = THREE.MathUtils.lerp(
      -Math.PI / 4, // Starting rotation (more dramatic)
      Math.PI / 4,  // Ending rotation
      scrollProgress
    );

    modelRef.current.rotation.y = THREE.MathUtils.lerp(
      modelRef.current.rotation.y,
      targetRotationY,
      0.03 // Slower interpolation for smoother movement
    );

    // Enhanced floating animation with scroll influence
    const baseY = -1.5;
    const floatAmplitude = 0.1;
    const scrollInfluence = scrollProgress * 0.5; // Move up as user scrolls
    modelRef.current.position.y = baseY + Math.sin(time * 0.8) * floatAmplitude + scrollInfluence;

    // Add subtle scale animation based on scroll
    const scaleVariation = 1.2 + Math.sin(scrollProgress * Math.PI) * 0.1;
    modelRef.current.scale.setScalar(scaleVariation);

    // Add rotation on X-axis for more dynamic movement
    const targetRotationX = Math.sin(scrollProgress * Math.PI * 2) * 0.1;
    modelRef.current.rotation.x = THREE.MathUtils.lerp(
      modelRef.current.rotation.x,
      targetRotationX,
      0.02
    );
    
    // Enhanced head tracking for mouse position with scroll influence
    if (animationState.headTracking && nodes) {
      // Find the head bone/node with more comprehensive search
      const headBone = modelRef.current.getObjectByName('Head') ||
                      modelRef.current.getObjectByName('head') ||
                      modelRef.current.getObjectByName('mixamorigHead') ||
                      modelRef.current.getObjectByName('Neck') ||
                      modelRef.current.getObjectByName('neck');

      if (headBone) {
        // Calculate target rotation based on mouse position with scroll dampening
        const scrollDamping = 1 - (scrollProgress * 0.3); // Reduce head movement as user scrolls

        const targetRotationX = THREE.MathUtils.lerp(
          -0.3, // Max left (increased range)
          0.3,  // Max right
          mousePosition.x
        ) * scrollDamping;

        const targetRotationY = THREE.MathUtils.lerp(
          0.3,  // Max down (increased range)
          -0.3, // Max up
          mousePosition.y
        ) * scrollDamping;

        // Apply smooth rotation to head with improved interpolation
        headBone.rotation.x = THREE.MathUtils.lerp(
          headBone.rotation.x,
          targetRotationY,
          0.03 // Slower for more natural movement
        );

        headBone.rotation.y = THREE.MathUtils.lerp(
          headBone.rotation.y,
          targetRotationX,
          0.03
        );
      }
    }

    // Add arm movement based on scroll
    const rightArm = modelRef.current.getObjectByName('RightArm') ||
                    modelRef.current.getObjectByName('right_arm') ||
                    modelRef.current.getObjectByName('mixamorigRightArm');

    const leftArm = modelRef.current.getObjectByName('LeftArm') ||
                   modelRef.current.getObjectByName('left_arm') ||
                   modelRef.current.getObjectByName('mixamorigLeftArm');

    if (rightArm) {
      // Right arm moves based on scroll progress
      const armRotation = Math.sin(scrollProgress * Math.PI * 2) * 0.2;
      (rightArm as THREE.Object3D).rotation.z = armRotation;
    }

    if (leftArm) {
      // Left arm moves opposite to right arm
      const armRotation = -Math.sin(scrollProgress * Math.PI * 2) * 0.15;
      (leftArm as THREE.Object3D).rotation.z = armRotation;
    }
  });
  
  // Trigger waving animation when mouse is in top-right corner or at certain scroll positions
  useEffect(() => {
    const scrollProgress = scrollYProgress.get();
    const shouldWave = (mousePosition.x > 0.8 && mousePosition.y < 0.2) ||
                      (scrollProgress > 0.1 && scrollProgress < 0.3); // Wave during about section

    if (shouldWave && !animationState.waving) {
      setAnimationState(prev => ({ ...prev, waving: true }));

      // Reset after animation
      setTimeout(() => {
        setAnimationState(prev => ({ ...prev, waving: false }));
      }, 3000); // Longer duration for better effect
    }
  }, [mousePosition, animationState.waving, scrollYProgress]);
  
  // Apply enhanced waving animation
  useEffect(() => {
    if (!modelRef.current) return;

    const rightArm = modelRef.current.getObjectByName('RightArm') ||
                    modelRef.current.getObjectByName('right_arm') ||
                    modelRef.current.getObjectByName('mixamorigRightArm');

    const rightForearm = modelRef.current.getObjectByName('RightForeArm') ||
                        modelRef.current.getObjectByName('right_forearm') ||
                        modelRef.current.getObjectByName('mixamorigRightForeArm');

    if (rightArm && animationState.waving) {
      let startTime = Date.now();

      // Enhanced waving animation with forearm movement
      const waveAnimation = () => {
        const elapsed = (Date.now() - startTime) / 1000;
        const waveFrequency = 4; // Waves per second
        const waveAmplitude = 0.5;

        // Main arm movement
        (rightArm as THREE.Object3D).rotation.z = Math.sin(elapsed * waveFrequency * Math.PI * 2) * waveAmplitude;
        (rightArm as THREE.Object3D).rotation.x = Math.sin(elapsed * waveFrequency * Math.PI * 2 + Math.PI/4) * 0.2;

        // Forearm movement for more natural waving
        if (rightForearm) {
          (rightForearm as THREE.Object3D).rotation.z = Math.sin(elapsed * waveFrequency * Math.PI * 2 + Math.PI/2) * 0.3;
        }

        if (animationState.waving) {
          requestAnimationFrame(waveAnimation);
        } else {
          // Smooth reset to original position
          const resetAnimation = () => {
            (rightArm as THREE.Object3D).rotation.z *= 0.9;
            (rightArm as THREE.Object3D).rotation.x *= 0.9;
            if (rightForearm) {
              (rightForearm as THREE.Object3D).rotation.z *= 0.9;
            }

            if (Math.abs((rightArm as THREE.Object3D).rotation.z) > 0.01) {
              requestAnimationFrame(resetAnimation);
            }
          };
          resetAnimation();
        }
      };

      requestAnimationFrame(waveAnimation);
    }
  }, [animationState.waving]);
  
  if (!scene) {
    return null; // Don't render anything if scene is not loaded
  }

  return (
    <group ref={modelRef} position={[0, -1.5, 0]} scale={1.2}>
      <primitive object={scene} />
    </group>
  );
}

// Main scene component
const ModelScene = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0.5, y: 0.5 });
  const { scrollYProgress } = useScroll();
  
  // Track mouse position
  useEffect(() => {
    const handleMouseMove = (event: MouseEvent) => {
      setMousePosition({
        x: event.clientX / window.innerWidth,
        y: event.clientY / window.innerHeight
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);
  
  return (
    <div className="absolute inset-0 z-0">
      <Canvas
        camera={{ position: [2, 1, 4], fov: 50 }}
        shadows
        gl={{
          antialias: true,
          alpha: true,
          powerPreference: "high-performance"
        }}
        dpr={[1, 2]}
      >
        {/* Improved lighting setup */}
        <ambientLight intensity={0.4} />
        <directionalLight
          position={[10, 10, 5]}
          intensity={1}
          castShadow
          shadow-mapSize={[1024, 1024]}
          shadow-camera-far={50}
          shadow-camera-left={-10}
          shadow-camera-right={10}
          shadow-camera-top={10}
          shadow-camera-bottom={-10}
        />
        <pointLight
          position={[-10, -10, -10]}
          intensity={0.3}
          color="#00ff9e"
        />
        <spotLight
          position={[0, 10, 0]}
          angle={0.3}
          penumbra={1}
          intensity={0.5}
          color="#ffffff"
        />

        <RobotModel scrollYProgress={scrollYProgress} mousePosition={mousePosition} />
        <Environment preset="sunset" />
      </Canvas>
    </div>
  );
};

export default ModelScene;