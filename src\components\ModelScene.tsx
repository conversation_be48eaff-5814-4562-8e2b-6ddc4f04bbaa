import { useRef } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { useGLTF, Environment } from '@react-three/drei';
import * as THREE from 'three';
import { useScroll } from 'framer-motion';

// Simple robot model component
function RobotModel() {
  const modelRef = useRef<THREE.Group>(null);
  const { scrollYProgress } = useScroll();
  const gltf = useGLTF('/models/sci-fi-robot.glb');

  // Simple scroll-based animation
  useFrame((state) => {
    if (!modelRef.current) return;

    const scrollProgress = scrollYProgress.get();
    const time = state.clock.elapsedTime;

    // Rotate based on scroll
    modelRef.current.rotation.y = scrollProgress * Math.PI * 0.5;

    // Float animation
    modelRef.current.position.y = -1 + Math.sin(time) * 0.1;

    // Scale slightly based on scroll
    const scale = 1 + scrollProgress * 0.2;
    modelRef.current.scale.setScalar(scale);
  });

  if (!gltf?.scene) {
    return (
      <group>
        <mesh position={[0, 0, 0]}>
          <boxGeometry args={[1, 2, 0.5]} />
          <meshStandardMaterial color="#00ff9e" />
        </mesh>
        <mesh position={[0, 1.2, 0]}>
          <sphereGeometry args={[0.3]} />
          <meshStandardMaterial color="#00ff9e" />
        </mesh>
      </group>
    );
  }

  return (
    <group ref={modelRef} position={[0, -1, 0]} scale={1.5}>
      <primitive object={gltf.scene} />
    </group>
  );
}

// Main scene component
const ModelScene = () => {
  return (
    <div className="absolute inset-0 z-0">
      <Canvas
        camera={{ position: [4, 3, 6], fov: 50 }}
        gl={{
          antialias: true,
          alpha: true,
          powerPreference: "high-performance"
        }}
      >
        {/* Enhanced lighting setup */}
        <ambientLight intensity={0.4} color="#ffffff" />
        <directionalLight
          position={[10, 10, 5]}
          intensity={1.2}
          color="#ffffff"
          castShadow
        />
        <pointLight
          position={[-5, 5, 5]}
          intensity={0.8}
          color="#00ff9e"
        />
        <pointLight
          position={[5, -2, 3]}
          intensity={0.4}
          color="#4f46e5"
        />

        <RobotModel />
        <Environment preset="city" background={false} />
      </Canvas>
    </div>
  );
};

// Preload the model
useGLTF.preload('/models/sci-fi-robot.glb');

export default ModelScene;
