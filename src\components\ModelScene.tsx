import { useRef, useState, useEffect } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { useFBX, Environment } from '@react-three/drei';
import * as THREE from 'three';

interface RobotModelProps {
  mousePos: { x: number; y: number };
  scrollYProgress: number;
  currentSection: string;
}

function RobotModel({ mousePos, scrollYProgress, currentSection }: RobotModelProps) {
  const groupRef = useRef<THREE.Group>(null);
  const mixerRef = useRef<THREE.AnimationMixer | null>(null);
  const [modelReady, setModelReady] = useState(false);
  const [animationActions, setAnimationActions] = useState<THREE.AnimationAction[]>([]);
  const [bones, setBones] = useState<{ [key: string]: THREE.Bone }>({});

  // Load FBX model
  const fbx = useFBX('/models/sci-fi-robot.fbx');

  // Initialize model when FBX loads
  useEffect(() => {
    if (fbx && groupRef.current) {
      console.log('FBX Model loaded:', fbx);

      // Clear any existing children
      while (groupRef.current.children.length > 0) {
        groupRef.current.remove(groupRef.current.children[0]);
      }

      // Clone the FBX to avoid modifying the cached version
      const clonedFBX = fbx.clone();

      // Set up the model with much larger scale
      clonedFBX.scale.setScalar(0.05); // Doubled the size from 0.025 to 0.05
      clonedFBX.position.set(0, -1.5, 0);
      clonedFBX.rotation.set(0, 0, 0);

      // Find and store bones for manual animation
      const foundBones: { [key: string]: THREE.Bone } = {};
      clonedFBX.traverse((child: any) => {
        if (child.isBone) {
          foundBones[child.name] = child;
        }

        if (child.isMesh) {
          child.castShadow = true;
          child.receiveShadow = true;

          // Ensure materials are visible
          if (child.material) {
            if (Array.isArray(child.material)) {
              child.material.forEach((mat: any) => {
                mat.needsUpdate = true;
                if (mat.map) mat.map.needsUpdate = true;
              });
            } else {
              child.material.needsUpdate = true;
              if (child.material.map) child.material.map.needsUpdate = true;
            }
          }
        }
      });

      setBones(foundBones);
      console.log('Found bones:', Object.keys(foundBones));

      // Add to group
      groupRef.current.add(clonedFBX);

      // Set up animation mixer if animations exist
      if (fbx.animations && fbx.animations.length > 0) {
        const mixer = new THREE.AnimationMixer(clonedFBX);
        mixerRef.current = mixer;

        const actions = fbx.animations.map((clip) => {
          const action = mixer.clipAction(clip);
          action.play();
          action.setEffectiveWeight(1);
          return action;
        });

        setAnimationActions(actions);
        console.log(`Loaded ${fbx.animations.length} animations`);
      }

      setModelReady(true);
      console.log('Model setup complete');
    }
  }, [fbx]);

  // Helper function to get section-based positioning
  const getSectionOffset = (section: string, progress: number) => {
    const positions = {
      hero: { x: 0, z: 0 },
      about: { x: -0.3, z: 0.1 },
      skills: { x: 0.2, z: -0.1 },
      experience: { x: -0.1, z: 0.2 },
      projects: { x: 0.3, z: 0.1 },
      contact: { x: 0, z: 0.1 }
    };

    const basePos = positions[section as keyof typeof positions] || { x: 0, z: 0 };
    return {
      x: basePos.x + Math.sin(progress * Math.PI * 2) * 0.05,
      z: basePos.z + Math.cos(progress * Math.PI * 2) * 0.05
    };
  };

  // Animation loop with enhanced scroll-based effects and bone manipulation
  useFrame((state, delta) => {
    if (!groupRef.current || !modelReady) return;

    // Update animation mixer
    if (mixerRef.current) {
      mixerRef.current.update(delta);
    }

    // Control animation speed based on scroll and section
    if (animationActions.length > 0) {
      animationActions.forEach((action) => {
        // Different animation speeds for different sections
        let speedMultiplier = 1;
        switch (currentSection) {
          case 'hero':
            speedMultiplier = 1.0;
            break;
          case 'about':
            speedMultiplier = 1.3;
            break;
          case 'skills':
            speedMultiplier = 1.8;
            break;
          case 'projects':
            speedMultiplier = 2.2;
            break;
          default:
            speedMultiplier = 1.0;
        }
        action.timeScale = speedMultiplier * (0.8 + scrollYProgress * 1.2);
      });
    }

    const time = state.clock.getElapsedTime();

    // Enhanced mouse-based rotation with section influence
    const mouseInfluence = currentSection === 'hero' ? 1 : 0.6;
    const targetRotationY = THREE.MathUtils.lerp(
      -Math.PI / 3,
      Math.PI / 3,
      (mousePos.x + 1) / 2
    ) * mouseInfluence;

    const targetRotationX = THREE.MathUtils.lerp(
      -Math.PI / 6,
      Math.PI / 6,
      (mousePos.y + 1) / 2
    ) * mouseInfluence;

    // Apply smooth rotation
    groupRef.current.rotation.y = THREE.MathUtils.lerp(
      groupRef.current.rotation.y,
      targetRotationY + Math.sin(time * 0.4) * 0.1,
      0.03
    );

    groupRef.current.rotation.x = THREE.MathUtils.lerp(
      groupRef.current.rotation.x,
      targetRotationX,
      0.03
    );

    // Enhanced floating animation
    const floatAmplitude = currentSection === 'hero' ? 0.2 : 0.12;
    groupRef.current.position.y = -1.5 + Math.sin(time * 0.9) * floatAmplitude;

    // Dynamic scroll-based scaling - much larger
    const baseScale = 0.05; // Doubled from 0.025
    const scrollScale = 1 + scrollYProgress * 1.2; // More dramatic scaling
    groupRef.current.scale.setScalar(baseScale * scrollScale);

    // Section-based positioning
    const sectionOffset = getSectionOffset(currentSection, scrollYProgress);
    groupRef.current.position.x = sectionOffset.x;
    groupRef.current.position.z = sectionOffset.z;

    // Enhanced scroll-based rotation
    const scrollRotation = scrollYProgress * Math.PI * 0.6;
    groupRef.current.rotation.z = Math.sin(scrollRotation) * 0.08;

    // Bone-based animations for different sections
    if (Object.keys(bones).length > 0) {
      // Head movement based on mouse
      const headBone = bones['Head'] || bones['head'] || bones['mixamorigHead'];
      if (headBone) {
        headBone.rotation.y = THREE.MathUtils.lerp(
          headBone.rotation.y,
          mousePos.x * 0.3,
          0.05
        );
        headBone.rotation.x = THREE.MathUtils.lerp(
          headBone.rotation.x,
          -mousePos.y * 0.2,
          0.05
        );
      }

      // Arm movements based on section
      const leftArm = bones['LeftArm'] || bones['leftArm'] || bones['mixamorigLeftArm'];
      const rightArm = bones['RightArm'] || bones['rightArm'] || bones['mixamorigRightArm'];

      if (leftArm && rightArm) {
        switch (currentSection) {
          case 'skills':
            leftArm.rotation.z = Math.sin(time * 2) * 0.3;
            rightArm.rotation.z = -Math.sin(time * 2) * 0.3;
            break;
          case 'projects':
            leftArm.rotation.x = Math.sin(time * 1.5) * 0.2;
            rightArm.rotation.x = Math.sin(time * 1.5 + Math.PI) * 0.2;
            break;
          default:
            // Subtle breathing motion
            leftArm.rotation.z = Math.sin(time * 0.8) * 0.1;
            rightArm.rotation.z = -Math.sin(time * 0.8) * 0.1;
        }
      }
    }

    // Add some dynamic movement based on section
    if (currentSection === 'skills') {
      groupRef.current.rotation.y += Math.sin(time * 1.5) * 0.005;
    } else if (currentSection === 'projects') {
      groupRef.current.position.x += Math.sin(time * 1.2) * 0.03;
    }
  });

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (mixerRef.current) {
        mixerRef.current.stopAllAction();
        mixerRef.current = null;
      }
    };
  }, []);

  // Render fallback if model not ready
  if (!modelReady) {
    return (
      <group ref={groupRef} scale={2}>
        <mesh position={[0, 0, 0]}>
          <boxGeometry args={[1.2, 2.4, 0.6]} />
          <meshStandardMaterial color="#00ff9e" />
        </mesh>
        <mesh position={[0, 1.4, 0]}>
          <sphereGeometry args={[0.4]} />
          <meshStandardMaterial color="#00ff9e" />
        </mesh>
        {/* Arms */}
        <mesh position={[-0.9, 0.5, 0]}>
          <boxGeometry args={[0.3, 1.2, 0.3]} />
          <meshStandardMaterial color="#00ff9e" />
        </mesh>
        <mesh position={[0.9, 0.5, 0]}>
          <boxGeometry args={[0.3, 1.2, 0.3]} />
          <meshStandardMaterial color="#00ff9e" />
        </mesh>
        {/* Legs */}
        <mesh position={[-0.4, -1.8, 0]}>
          <boxGeometry args={[0.35, 1.5, 0.35]} />
          <meshStandardMaterial color="#00ff9e" />
        </mesh>
        <mesh position={[0.4, -1.8, 0]}>
          <boxGeometry args={[0.35, 1.5, 0.35]} />
          <meshStandardMaterial color="#00ff9e" />
        </mesh>
      </group>
    );
  }

  return <group ref={groupRef} />;
}

interface ModelSceneProps {
  currentSection?: string;
  className?: string;
}

export default function ModelScene({ currentSection = 'hero', className = '' }: ModelSceneProps) {
  const [mousePos, setMousePos] = useState({ x: 0, y: 0 });
  const [scrollYProgress, setScrollYProgress] = useState(0);

  // Track scroll position
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollPercent = docHeight > 0 ? scrollTop / docHeight : 0;
      setScrollYProgress(Math.min(Math.max(scrollPercent, 0), 1));
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll();

    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Track mouse position
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePos({
        x: (e.clientX / window.innerWidth) * 2 - 1,
        y: -(e.clientY / window.innerHeight) * 2 + 1,
      });
    };

    window.addEventListener('mousemove', handleMouseMove, { passive: true });

    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return (
    <div className={`w-full h-full ${className}`}>
      <Canvas
        shadows
        dpr={[1, 2]}
        camera={{ position: [4, 3, 6], fov: 45 }}
        gl={{ antialias: true, alpha: true, powerPreference: "high-performance" }}
      >
        {/* Enhanced lighting setup for better visibility */}
        <ambientLight intensity={0.8} />
        <directionalLight
          position={[8, 12, 8]}
          intensity={2.0}
          castShadow
          shadow-mapSize={[2048, 2048]}
          shadow-camera-far={50}
          shadow-camera-left={-20}
          shadow-camera-right={20}
          shadow-camera-top={20}
          shadow-camera-bottom={-20}
        />
        <pointLight position={[-8, 8, 8]} intensity={1.2} color="#00ff9e" />
        <pointLight position={[8, -8, -8]} intensity={0.8} color="#0066ff" />
        <spotLight
          position={[0, 15, 5]}
          angle={0.3}
          penumbra={1}
          intensity={1.5}
          color="#ffffff"
        />

        <RobotModel
          mousePos={mousePos}
          scrollYProgress={scrollYProgress}
          currentSection={currentSection}
        />
        <Environment preset="city" />
      </Canvas>
    </div>
  );
}

// Preload the FBX model
useFBX.preload('/models/sci-fi-robot.fbx');