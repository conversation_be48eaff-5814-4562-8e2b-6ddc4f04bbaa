import { useRef, useState, useEffect } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { useFBX, Environment } from '@react-three/drei';
import * as THREE from 'three';

interface RobotModelProps {
  mousePos: { x: number; y: number };
  scrollYProgress: number;
  currentSection: string;
}

function RobotModel({ mousePos, scrollYProgress, currentSection }: RobotModelProps) {
  const groupRef = useRef<THREE.Group>(null);
  const mixerRef = useRef<THREE.AnimationMixer | null>(null);
  const [modelReady, setModelReady] = useState(false);
  const [animationActions, setAnimationActions] = useState<THREE.AnimationAction[]>([]);
  // const { viewport } = useThree(); // For future responsive scaling

  // Load FBX model
  const fbx = useFBX('/models/sci-fi-robot.fbx');

  // Initialize model when FBX loads
  useEffect(() => {
    if (fbx && groupRef.current) {
      console.log('FBX Model loaded:', fbx);

      // Clear any existing children
      while (groupRef.current.children.length > 0) {
        groupRef.current.remove(groupRef.current.children[0]);
      }

      // Clone the FBX to avoid modifying the cached version
      const clonedFBX = fbx.clone();

      // Set up the model with larger scale
      clonedFBX.scale.setScalar(0.025); // Increased size significantly
      clonedFBX.position.set(0, -1, 0);
      clonedFBX.rotation.set(0, 0, 0);

      // Configure materials and shadows
      clonedFBX.traverse((child: any) => {
        if (child.isMesh) {
          child.castShadow = true;
          child.receiveShadow = true;

          // Ensure materials are visible
          if (child.material) {
            if (Array.isArray(child.material)) {
              child.material.forEach((mat: any) => {
                mat.needsUpdate = true;
                if (mat.map) mat.map.needsUpdate = true;
              });
            } else {
              child.material.needsUpdate = true;
              if (child.material.map) child.material.map.needsUpdate = true;
            }
          }
        }
      });

      // Add to group
      groupRef.current.add(clonedFBX);

      // Set up animation mixer if animations exist
      if (fbx.animations && fbx.animations.length > 0) {
        const mixer = new THREE.AnimationMixer(clonedFBX);
        mixerRef.current = mixer;

        const actions = fbx.animations.map((clip) => {
          const action = mixer.clipAction(clip);
          action.play();
          return action;
        });

        setAnimationActions(actions);
        console.log(`Loaded ${fbx.animations.length} animations`);
      }

      setModelReady(true);
      console.log('Model setup complete');
    }
  }, [fbx]);

  // Helper function to get section-based positioning
  const getSectionOffset = (section: string, progress: number) => {
    const positions = {
      hero: { x: 0, z: 0 },
      about: { x: -0.5, z: 0.2 },
      skills: { x: 0.3, z: -0.1 },
      experience: { x: -0.2, z: 0.3 },
      projects: { x: 0.4, z: 0.1 },
      contact: { x: 0, z: 0.2 }
    };

    const basePos = positions[section as keyof typeof positions] || { x: 0, z: 0 };
    return {
      x: basePos.x + Math.sin(progress * Math.PI * 2) * 0.1,
      z: basePos.z + Math.cos(progress * Math.PI * 2) * 0.1
    };
  };

  // Animation loop with enhanced scroll-based effects
  useFrame((state, delta) => {
    if (!groupRef.current || !modelReady) return;

    // Update animation mixer
    if (mixerRef.current) {
      mixerRef.current.update(delta);
    }

    // Control animation speed based on scroll and section
    if (animationActions.length > 0) {
      animationActions.forEach((action) => {
        // Different animation speeds for different sections
        let speedMultiplier = 1;
        switch (currentSection) {
          case 'hero':
            speedMultiplier = 0.8;
            break;
          case 'about':
            speedMultiplier = 1.2;
            break;
          case 'skills':
            speedMultiplier = 1.5;
            break;
          case 'projects':
            speedMultiplier = 2.0;
            break;
          default:
            speedMultiplier = 1.0;
        }
        action.timeScale = speedMultiplier * (0.5 + scrollYProgress * 1.5);
      });
    }

    const time = state.clock.getElapsedTime();

    // Enhanced mouse-based rotation with section influence
    const mouseInfluence = currentSection === 'hero' ? 1 : 0.5;
    const targetRotationY = THREE.MathUtils.lerp(
      -Math.PI / 4,
      Math.PI / 4,
      (mousePos.x + 1) / 2
    ) * mouseInfluence;

    const targetRotationX = THREE.MathUtils.lerp(
      -Math.PI / 8,
      Math.PI / 8,
      (mousePos.y + 1) / 2
    ) * mouseInfluence;

    // Apply smooth rotation
    groupRef.current.rotation.y = THREE.MathUtils.lerp(
      groupRef.current.rotation.y,
      targetRotationY + Math.sin(time * 0.3) * 0.1,
      0.02
    );

    groupRef.current.rotation.x = THREE.MathUtils.lerp(
      groupRef.current.rotation.x,
      targetRotationX,
      0.02
    );

    // Enhanced floating animation
    const floatAmplitude = currentSection === 'hero' ? 0.15 : 0.08;
    groupRef.current.position.y = -1 + Math.sin(time * 0.8) * floatAmplitude;

    // Dynamic scroll-based scaling
    const baseScale = 0.025; // Increased base scale
    const scrollScale = 1 + scrollYProgress * 0.8; // More dramatic scaling
    groupRef.current.scale.setScalar(baseScale * scrollScale);

    // Section-based positioning
    const sectionOffset = getSectionOffset(currentSection, scrollYProgress);
    groupRef.current.position.x = sectionOffset.x;
    groupRef.current.position.z = sectionOffset.z;

    // Enhanced scroll-based rotation
    const scrollRotation = scrollYProgress * Math.PI * 0.5;
    groupRef.current.rotation.z = Math.sin(scrollRotation) * 0.1;

    // Add some dynamic movement based on section
    if (currentSection === 'skills') {
      groupRef.current.rotation.y += Math.sin(time * 2) * 0.01;
    } else if (currentSection === 'projects') {
      groupRef.current.position.x += Math.sin(time * 1.5) * 0.05;
    }
  });

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (mixerRef.current) {
        mixerRef.current.stopAllAction();
        mixerRef.current = null;
      }
    };
  }, []);

  // Render fallback if model not ready
  if (!modelReady) {
    return (
      <group ref={groupRef}>
        <mesh position={[0, 0, 0]}>
          <boxGeometry args={[0.8, 1.6, 0.4]} />
          <meshStandardMaterial color="#00ff9e" />
        </mesh>
        <mesh position={[0, 0.9, 0]}>
          <sphereGeometry args={[0.25]} />
          <meshStandardMaterial color="#00ff9e" />
        </mesh>
        {/* Arms */}
        <mesh position={[-0.6, 0.3, 0]}>
          <boxGeometry args={[0.2, 0.8, 0.2]} />
          <meshStandardMaterial color="#00ff9e" />
        </mesh>
        <mesh position={[0.6, 0.3, 0]}>
          <boxGeometry args={[0.2, 0.8, 0.2]} />
          <meshStandardMaterial color="#00ff9e" />
        </mesh>
        {/* Legs */}
        <mesh position={[-0.3, -1.2, 0]}>
          <boxGeometry args={[0.25, 1.0, 0.25]} />
          <meshStandardMaterial color="#00ff9e" />
        </mesh>
        <mesh position={[0.3, -1.2, 0]}>
          <boxGeometry args={[0.25, 1.0, 0.25]} />
          <meshStandardMaterial color="#00ff9e" />
        </mesh>
      </group>
    );
  }

  return <group ref={groupRef} />;
}

interface ModelSceneProps {
  currentSection?: string;
  className?: string;
}

export default function ModelScene({ currentSection = 'hero', className = '' }: ModelSceneProps) {
  const [mousePos, setMousePos] = useState({ x: 0, y: 0 });
  const [scrollYProgress, setScrollYProgress] = useState(0);

  // Track scroll position
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollPercent = docHeight > 0 ? scrollTop / docHeight : 0;
      setScrollYProgress(Math.min(Math.max(scrollPercent, 0), 1));
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll();

    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Track mouse position
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePos({
        x: (e.clientX / window.innerWidth) * 2 - 1,
        y: -(e.clientY / window.innerHeight) * 2 + 1,
      });
    };

    window.addEventListener('mousemove', handleMouseMove, { passive: true });

    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return (
    <div className={`w-full h-full ${className}`}>
      <Canvas
        shadows
        dpr={[1, 2]}
        camera={{ position: [3, 2, 5], fov: 50 }}
        gl={{ antialias: true, alpha: true }}
      >
        {/* Enhanced lighting setup for better visibility */}
        <ambientLight intensity={0.7} />
        <directionalLight
          position={[5, 10, 5]}
          intensity={1.8}
          castShadow
          shadow-mapSize={[2048, 2048]}
          shadow-camera-far={50}
          shadow-camera-left={-15}
          shadow-camera-right={15}
          shadow-camera-top={15}
          shadow-camera-bottom={-15}
        />
        <pointLight position={[-5, 5, 5]} intensity={1.0} color="#00ff9e" />
        <pointLight position={[5, -5, -5]} intensity={0.6} color="#0066ff" />
        <spotLight
          position={[0, 10, 3]}
          angle={0.4}
          penumbra={1}
          intensity={1.2}
          color="#ffffff"
        />

        <RobotModel
          mousePos={mousePos}
          scrollYProgress={scrollYProgress}
          currentSection={currentSection}
        />
        <Environment preset="sunset" />
      </Canvas>
    </div>
  );
}

// Preload the FBX model
useFBX.preload('/models/sci-fi-robot.fbx');