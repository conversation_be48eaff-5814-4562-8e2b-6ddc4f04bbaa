import { useRef, useState, useEffect, useMemo } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { useFBX } from '@react-three/drei';
import * as THREE from 'three';
import { useMotionValue, useSpring } from 'framer-motion';

interface RobotModelProps {
  activeSection: string;
  mousePosition: { x: number; y: number };
  scale: number;
}

// Advanced robot model component with animations
function RobotModel({ activeSection, mousePosition, scale }: RobotModelProps) {
  const modelRef = useRef<THREE.Group>(null);
  const headRef = useRef<THREE.Group>(null);


  // Comprehensive bone references for detailed animation
  const bonesRef = useRef<{
    head?: THREE.Object3D;
    neck?: THREE.Object3D;
    spine?: THREE.Object3D;

    // Arms and hands
    leftShoulder?: THREE.Object3D;
    rightShoulder?: THREE.Object3D;
    leftArm?: THREE.Object3D;
    rightArm?: THREE.Object3D;
    leftForearm?: THREE.Object3D;
    rightForearm?: THREE.Object3D;
    leftHand?: THREE.Object3D;
    rightHand?: THREE.Object3D;

    // Legs and feet
    leftThigh?: THREE.Object3D;
    rightThigh?: THREE.Object3D;
    leftLeg?: THREE.Object3D;
    rightLeg?: THREE.Object3D;
    leftFoot?: THREE.Object3D;
    rightFoot?: THREE.Object3D;

    // Fingers (if available)
    leftFingers?: THREE.Object3D[];
    rightFingers?: THREE.Object3D[];
  }>({});

  // Store target rotations for smooth transitions
  const targetRotations = useRef<{[boneName: string]: {x: number, y: number, z: number}}>({});
  const currentRotations = useRef<{[boneName: string]: {x: number, y: number, z: number}}>({});

  // Mouse tracking with spring physics
  const mouseX = useMotionValue(0);
  const mouseY = useMotionValue(0);
  const springMouseX = useSpring(mouseX, { stiffness: 100, damping: 20 });
  const springMouseY = useSpring(mouseY, { stiffness: 100, damping: 20 });

  // Load your actual FBX model
  let fbx: any = null;
  try {
    fbx = useFBX('/models/sci-fi-robot.fbx');
    console.log('🤖 Your sci-fi-robot.fbx loaded successfully!');
    console.log('📊 Available animations:', fbx?.animations?.length || 0);

    // Log animation names for debugging
    if (fbx?.animations) {
      fbx.animations.forEach((clip: any, index: number) => {
        console.log(`🎬 Animation ${index}:`, clip.name);
      });
    }

    // Log model structure for debugging
    console.log('🏗️ FBX model structure:', fbx);
    if (fbx.children) {
      console.log('👶 Model children:', fbx.children.length);
      fbx.children.forEach((child: any, index: number) => {
        console.log(`Child ${index}:`, child.name, child.type);
      });
    }
  } catch (error) {
    console.error('❌ Error loading your sci-fi-robot.fbx:', error);
    console.log('🔄 Falling back to large geometric model');
  }

  // Update mouse position
  useEffect(() => {
    mouseX.set(mousePosition.x);
    mouseY.set(mousePosition.y);
  }, [mousePosition, mouseX, mouseY]);

  // Custom hand movements and gestures instead of default animations
  useEffect(() => {
    console.log(`🎭 Setting up custom gestures for section: ${activeSection}`);
  }, [activeSection]);

  // Position based on section layout - optimized for centralized model
  const sectionPositions = useMemo(() => ({
    hero: { x: 4, y: 0, z: 0 },         // Right side for hero
    about: { x: -4, y: 0, z: 0 },       // Left side for about
    skills: { x: 4.5, y: 0.5, z: 0.5 }, // Right side for skills
    experience: { x: -3.5, y: 0, z: 0 }, // Left side for experience
    projects: { x: 5, y: 0.5, z: -0.5 }, // Right side for projects (largest)
    contact: { x: 3, y: 0, z: 1 }       // Right side for contact
  }), []);

  // Advanced animation frame
  useFrame((state) => {
    if (!modelRef.current) return;

    const time = state.clock.elapsedTime;

    // Get target position for current section
    const targetPos = sectionPositions[activeSection as keyof typeof sectionPositions] || { x: 0, y: 0, z: 0 };

    // Calculate viewport-relative positioning for responsive design
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // Adjust position based on viewport size (reference: 1920x1080)
    const responsiveX = targetPos.x * (viewportWidth / 1920);
    const responsiveY = targetPos.y * (viewportHeight / 1080);

    // Smooth position transitions with floating animation
    const floatY = responsiveY + Math.sin(time * 1.2) * 0.3;

    // SMOOTHER TRANSITIONS - increased lerp speed for better responsiveness
    modelRef.current.position.x = THREE.MathUtils.lerp(modelRef.current.position.x, responsiveX, 0.08);
    modelRef.current.position.y = THREE.MathUtils.lerp(modelRef.current.position.y, floatY, 0.06);
    modelRef.current.position.z = THREE.MathUtils.lerp(modelRef.current.position.z, targetPos.z, 0.08);

    // Dynamic scaling with breathing effect - SMOOTHER
    const targetScale = scale * (1 + Math.sin(time * 0.8) * 0.08);
    modelRef.current.scale.setScalar(THREE.MathUtils.lerp(modelRef.current.scale.x, targetScale, 0.06));

    // Reduced logging frequency to avoid console spam
    if (Math.floor(time * 2) % 6 === 0) {
      console.log(`🎭 Section: ${activeSection} | Pos: x:${modelRef.current.position.x.toFixed(1)}, y:${modelRef.current.position.y.toFixed(1)} | Scale: ${modelRef.current.scale.x.toFixed(1)}`);
    }

    // Head tracking mouse cursor
    if (headRef.current) {
      const mouseXNorm = (springMouseX.get() - 0.5) * 0.3; // Reduced intensity for more natural movement
      const mouseYNorm = (springMouseY.get() - 0.5) * 0.2;

      // If we have a real head bone, use it
      const headBone = (headRef.current as any).headBone;
      if (headBone) {
        headBone.rotation.y = THREE.MathUtils.lerp(headBone.rotation.y, mouseXNorm, 0.03);
        headBone.rotation.x = THREE.MathUtils.lerp(headBone.rotation.x, -mouseYNorm, 0.03);
      } else {
        // Fallback to group rotation
        headRef.current.rotation.y = THREE.MathUtils.lerp(headRef.current.rotation.y, mouseXNorm, 0.05);
        headRef.current.rotation.x = THREE.MathUtils.lerp(headRef.current.rotation.x, -mouseYNorm, 0.05);
      }
    }

    // SMOOTH BONE-BASED ANIMATIONS with interpolation
    const bones = bonesRef.current;

    // Debug: Log available bones and test direct manipulation
    if (Object.keys(bones).length > 0 && Math.floor(time * 2) % 8 === 0) {
      const availableBones = Object.keys(bones).filter(key => bones[key as keyof typeof bones]);
      console.log('🦴 Animating bones for section:', activeSection, 'Available bones:', availableBones);

      // AGGRESSIVE DIRECT TEST: Try to rotate hands directly
      if (bones.rightHand) {
        console.log('🧪 DIRECT TEST: Rotating right hand directly');
        bones.rightHand.rotation.z = Math.sin(time * 2) * 1.0; // Larger rotation
        bones.rightHand.rotation.x = Math.cos(time * 1.5) * 0.5;
        console.log('🧪 Right hand rotation set to:', bones.rightHand.rotation.z.toFixed(2));
      } else {
        console.log('❌ Right hand bone not found for direct test');
      }

      if (bones.leftHand) {
        console.log('🧪 DIRECT TEST: Rotating left hand directly');
        bones.leftHand.rotation.z = Math.sin(time * 1.8) * -1.0; // Larger rotation
        bones.leftHand.rotation.x = Math.cos(time * 2.2) * 0.5;
        console.log('🧪 Left hand rotation set to:', bones.leftHand.rotation.z.toFixed(2));
      } else {
        console.log('❌ Left hand bone not found for direct test');
      }
    }

    // Helper function to smoothly animate bone rotation with DEBUG
    const animateBone = (bone: THREE.Object3D | undefined, targetX: number, targetY: number, targetZ: number, speed: number = 0.05, boneName: string = 'unknown') => {
      if (!bone) {
        if (Math.floor(time) % 3 === 0) {
          console.log(`❌ Bone ${boneName} is undefined - cannot animate`);
        }
        return;
      }

      // Store previous rotation for comparison
      const prevX = bone.rotation.x;
      const prevY = bone.rotation.y;
      const prevZ = bone.rotation.z;

      bone.rotation.x = THREE.MathUtils.lerp(bone.rotation.x, targetX, speed);
      bone.rotation.y = THREE.MathUtils.lerp(bone.rotation.y, targetY, speed);
      bone.rotation.z = THREE.MathUtils.lerp(bone.rotation.z, targetZ, speed);

      // Debug: Log if rotation actually changed
      const changed = Math.abs(bone.rotation.x - prevX) > 0.001 ||
                     Math.abs(bone.rotation.y - prevY) > 0.001 ||
                     Math.abs(bone.rotation.z - prevZ) > 0.001;

      if (changed && Math.floor(time * 4) % 8 === 0) {
        console.log(`🎭 ${boneName} animated: x:${bone.rotation.x.toFixed(2)}, y:${bone.rotation.y.toFixed(2)}, z:${bone.rotation.z.toFixed(2)}`);
      }
    };

    // SIMPLE BONE TEST - Direct manipulation

    // Test if we have any bones at all
    const boneNames = Object.keys(bones).filter(key => bones[key as keyof typeof bones]);
    if (boneNames.length > 0 && Math.floor(time * 2) % 4 === 0) {
      console.log('🦴 Available bones:', boneNames);

      // Debug: Just observe what bones are available
      if (bones.rightArm) {
        console.log('✅ Right arm bone available for future animation');
      }
      if (bones.leftArm) {
        console.log('✅ Left arm bone available for future animation');
      }
    }

    // CAREFUL ANIMATIONS - PRESERVE NATURAL LOOK
    // Very subtle movements that enhance the natural pose without breaking it

    // STEP 1: Gentle breathing animation (affects whole upper body subtly)
    const breathingIntensity = Math.sin(time * 0.8) * 0.01; // Very small breathing motion

    // STEP 2: Section-based subtle enhancements
    switch (activeSection) {
      case 'hero':
        // HERO: Very subtle welcome gesture - just a tiny hand movement
        if (bones.rightHand) {
          bones.rightHand.rotation.z += Math.sin(time * 1.2) * 0.05; // Tiny wave motion
          bones.rightHand.rotation.x += breathingIntensity; // Breathing effect
        }
        if (bones.rightArm) {
          bones.rightArm.rotation.z += Math.sin(time * 0.6) * 0.02; // Very subtle arm movement
        }
        if (Math.floor(time * 2) % 10 === 0) {
          console.log('🎭 HERO: Subtle welcome gesture applied');
        }
        break;

      case 'about':
        // ABOUT: Minimal pointing enhancement - just slight hand orientation
        if (bones.leftHand) {
          bones.leftHand.rotation.x += Math.sin(time * 1.0) * 0.03; // Gentle pointing emphasis
          bones.leftHand.rotation.z += Math.sin(time * 1.5) * 0.02; // Subtle gesture
        }
        if (bones.leftArm) {
          bones.leftArm.rotation.y += Math.sin(time * 0.8) * 0.015; // Very slight arm adjustment
        }
        if (Math.floor(time * 2) % 10 === 0) {
          console.log('🎭 ABOUT: Subtle pointing enhancement applied');
        }
        break;

      case 'skills':
        // SKILLS: Gentle presentation gestures - both hands slightly active
        if (bones.rightHand) {
          bones.rightHand.rotation.z += Math.sin(time * 1.1) * 0.04; // Gentle presentation
          bones.rightHand.rotation.x += Math.sin(time * 1.3) * 0.025; // Natural movement
        }
        if (bones.leftHand) {
          bones.leftHand.rotation.z += Math.sin(time * 1.0 + Math.PI) * 0.035; // Opposite timing
          bones.leftHand.rotation.x += Math.sin(time * 1.2) * 0.02; // Coordinated movement
        }
        if (bones.rightArm) {
          bones.rightArm.rotation.z += Math.sin(time * 0.7) * 0.015; // Subtle arm support
        }
        if (bones.leftArm) {
          bones.leftArm.rotation.z += Math.sin(time * 0.8) * 0.012; // Gentle coordination
        }
        if (Math.floor(time * 2) % 10 === 0) {
          console.log('🎭 SKILLS: Gentle presentation gestures applied');
        }
        break;

      case 'experience':
        // EXPERIENCE: Professional confidence - minimal but purposeful
        if (bones.rightHand) {
          bones.rightHand.rotation.z += Math.sin(time * 0.9) * 0.02; // Confident posture
          bones.rightHand.rotation.x += breathingIntensity * 1.5; // Enhanced breathing
        }
        if (bones.leftHand) {
          bones.leftHand.rotation.z += Math.sin(time * 0.85) * 0.018; // Mirrored confidence
          bones.leftHand.rotation.x += breathingIntensity * 1.2; // Coordinated breathing
        }
        if (Math.floor(time * 2) % 10 === 0) {
          console.log('🎭 EXPERIENCE: Professional confidence applied');
        }
        break;

      case 'projects':
        // PROJECTS: Open, welcoming gestures - slightly more expressive
        if (bones.rightHand) {
          bones.rightHand.rotation.z += Math.sin(time * 1.0) * 0.045; // Open gesture
          bones.rightHand.rotation.y += Math.sin(time * 1.1) * 0.02; // Natural variation
        }
        if (bones.leftHand) {
          bones.leftHand.rotation.z += Math.sin(time * 0.95) * 0.04; // Welcoming motion
          bones.leftHand.rotation.y += Math.sin(time * 1.05) * 0.018; // Subtle variation
        }
        if (Math.floor(time * 2) % 10 === 0) {
          console.log('🎭 PROJECTS: Open welcoming gestures applied');
        }
        break;

      case 'contact':
        // CONTACT: Approachable, ready to connect
        if (bones.rightHand) {
          bones.rightHand.rotation.z += Math.sin(time * 1.15) * 0.035; // Friendly gesture
          bones.rightHand.rotation.x += Math.sin(time * 1.25) * 0.025; // Approachable movement
        }
        if (bones.leftHand) {
          bones.leftHand.rotation.z += Math.sin(time * 1.05) * 0.03; // Coordinated friendliness
          bones.leftHand.rotation.x += Math.sin(time * 1.2) * 0.022; // Natural flow
        }
        if (Math.floor(time * 2) % 10 === 0) {
          console.log('🎭 CONTACT: Approachable gestures applied');
        }
        break;

      default:
        // DEFAULT: Just breathing - very minimal life
        if (bones.rightHand) {
          bones.rightHand.rotation.x += breathingIntensity;
        }
        if (bones.leftHand) {
          bones.leftHand.rotation.x += breathingIntensity * 0.8;
        }
        if (Math.floor(time * 2) % 10 === 0) {
          console.log('🎭 DEFAULT: Gentle breathing animation applied');
        }
        break;
    }

    // STEP 3: Universal subtle head tracking (very gentle)
    if (bones.head) {
      const headTargetY = (mousePosition.x - 0.5) * 0.08; // Very gentle mouse following
      const headTargetX = -(mousePosition.y - 0.5) * 0.05; // Minimal range
      bones.head.rotation.x += headTargetX * 0.1; // Gradual movement
      bones.head.rotation.y += headTargetY * 0.1; // Smooth tracking
    }

    // STEP 4: Gentle spine breathing (adds life to the whole pose)
    if (bones.spine) {
      bones.spine.rotation.x += breathingIntensity * 0.5; // Very subtle spine movement
    }

    // Debug logging (reduced frequency)
    if (Math.floor(time * 2) % 8 === 0) {
      console.log(`🎭 Natural animations active for section: ${activeSection}`);
    }


    // If no bones found, provide feedback and add basic model animation
    if (Object.keys(bones).length === 0) {
      if (Math.floor(time * 2) % 10 === 0) {
        console.log('⚠️ No bones detected in FBX model - using basic model animation');
        console.log('💡 Check if your FBX model has a proper bone structure/armature');
      }
    }

    // Debug logging (reduced frequency)
    if (Math.floor(time * 2) % 8 === 0) {
      console.log(`🎭 Natural animations active for section: ${activeSection}`);
    }
  });

  // Simple bone detection and test
  useEffect(() => {
    if (!fbx) {
      console.log('❌ FBX model not loaded yet');
      return;
    }

    console.log('🤖 FBX LOADED! Testing bone detection...');
    console.log('🏗️ FBX children count:', fbx.children?.length || 0);

    const foundBones: any = {};
    const allObjects: any[] = [];
    const allObjectNames: string[] = [];
    let boneCount = 0;

    fbx.traverse((child: any) => {
        // Safety check for undefined child or name
        if (!child) {
          console.log('⚠️ Undefined child found in traverse, skipping');
          return;
        }

        // Store actual objects for manipulation
        allObjects.push(child);

        // Store names for debugging (with safety check)
        const childName = child.name || 'unnamed';
        const childType = child.type || 'unknown';
        allObjectNames.push(`${childName} (${childType})`);

        // EXACT BONE DETECTION for your Mixamo model
        if (child.isBone || child.type === 'Bone') {
          boneCount++;
          console.log(`🦴 Bone found: ${child.name} (${child.type})`);

          // IMMEDIATE TEST: Try to rotate hand bones directly
          if (child.name === 'mixamorigRightHand') {
            console.log('🧪 IMMEDIATE TEST: Found right hand bone, testing rotation');
            child.rotation.z = 1.0; // Set a visible rotation
            console.log('🧪 Right hand bone rotation set to 1.0');
          }
          if (child.name === 'mixamorigLeftHand') {
            console.log('🧪 IMMEDIATE TEST: Found left hand bone, testing rotation');
            child.rotation.x = 0.8; // Set a visible rotation
            console.log('🧪 Left hand bone rotation set to 0.8');
          }

          // Use exact bone names from your model.json
          switch (child.name) {
            // HEAD & NECK
            case 'mixamorigHead':
              foundBones.head = child;
              console.log(`✅ Head bone: ${child.name}`);
              break;
            case 'mixamorigNeck':
              foundBones.neck = child;
              console.log(`✅ Neck bone: ${child.name}`);
              break;

            // SPINE
            case 'mixamorigSpine':
            case 'mixamorigSpine1':
            case 'mixamorigSpine2':
              foundBones.spine = child; // Use the first spine bone found
              console.log(`✅ Spine bone: ${child.name}`);
              break;

            // LEFT ARM
            case 'mixamorigLeftShoulder':
              foundBones.leftShoulder = child;
              console.log(`✅ Left shoulder bone: ${child.name} - keeping default position`);
              break;
            case 'mixamorigLeftArm':
              foundBones.leftArm = child;
              console.log(`✅ Left arm bone: ${child.name} - keeping default position`);
              break;
            case 'mixamorigLeftForeArm':
              foundBones.leftForearm = child;
              console.log(`✅ Left forearm bone: ${child.name} - keeping default position`);
              break;
            case 'mixamorigLeftHand':
              foundBones.leftHand = child;
              console.log(`✅ Left hand bone: ${child.name} - keeping default position`);
              break;

            // RIGHT ARM
            case 'mixamorigRightShoulder':
              foundBones.rightShoulder = child;
              console.log(`✅ Right shoulder bone: ${child.name} - keeping default position`);
              break;
            case 'mixamorigRightArm':
              foundBones.rightArm = child;
              console.log(`✅ Right arm bone: ${child.name} - keeping default position`);
              break;
            case 'mixamorigRightForeArm':
              foundBones.rightForearm = child;
              console.log(`✅ Right forearm bone: ${child.name} - keeping default position`);
              break;
            case 'mixamorigRightHand':
              foundBones.rightHand = child;
              console.log(`✅ Right hand bone: ${child.name} - keeping default position`);
              break;

            // LEFT LEG
            case 'mixamorigLeftUpLeg':
              foundBones.leftThigh = child;
              console.log(`✅ Left thigh bone: ${child.name}`);
              break;
            case 'mixamorigLeftLeg':
              foundBones.leftLeg = child;
              console.log(`✅ Left leg bone: ${child.name}`);
              break;
            case 'mixamorigLeftFoot':
              foundBones.leftFoot = child;
              console.log(`✅ Left foot bone: ${child.name}`);
              break;

            // RIGHT LEG
            case 'mixamorigRightUpLeg':
              foundBones.rightThigh = child;
              console.log(`✅ Right thigh bone: ${child.name}`);
              break;
            case 'mixamorigRightLeg':
              foundBones.rightLeg = child;
              console.log(`✅ Right leg bone: ${child.name}`);
              break;
            case 'mixamorigRightFoot':
              foundBones.rightFoot = child;
              console.log(`✅ Right foot bone: ${child.name}`);
              break;

            default:
              // Log other bones for reference
              if (child.name.startsWith('mixamorig')) {
                console.log(`📝 Other Mixamo bone: ${child.name}`);
              }
              break;
          }
        }

        // Also check meshes that might be animatable
        if (child.isMesh) {
          console.log(`🎭 Mesh found: ${child.name || 'unnamed mesh'}`);

          // Safety check for undefined names
          if (!child.name) {
            console.log('⚠️ Mesh with undefined name found, skipping');
            return;
          }

          // Sometimes meshes are the animatable parts
          const childName = child.name.toLowerCase();
          if (childName.includes('hand') && !foundBones.leftHand && !foundBones.rightHand) {
            if (childName.includes('left')) {
              foundBones.leftHand = child;
              console.log(`✅ Left hand mesh: ${child.name}`);
            }
            if (childName.includes('right')) {
              foundBones.rightHand = child;
              console.log(`✅ Right hand mesh: ${child.name}`);
            }
          }
        }
      });

      console.log('All objects in your model:', allObjectNames);

      // Just log available objects for future reference - NO MODIFICATIONS
      console.log(`📊 Total objects in model: ${allObjects.length}`);
      const armRelatedObjects = allObjects.filter((obj: any) => {
        if (!obj || !obj.name) return false;
        const objName = obj.name.toLowerCase();
        return objName.includes('arm') || objName.includes('shoulder') || objName.includes('hand');
      });
      console.log(`🦾 Arm-related objects found: ${armRelatedObjects.length}`);
      armRelatedObjects.forEach((obj: any) => {
        console.log(`📍 ${obj.name} - keeping in default state`);
      });

      // Store all found bones - CRITICAL FIX
      bonesRef.current = {
        head: foundBones.head,
        neck: foundBones.neck,
        spine: foundBones.spine,
        leftShoulder: foundBones.leftShoulder,
        rightShoulder: foundBones.rightShoulder,
        leftArm: foundBones.leftArm,
        rightArm: foundBones.rightArm,
        leftForearm: foundBones.leftForearm,
        rightForearm: foundBones.rightForearm,
        leftHand: foundBones.leftHand,
        rightHand: foundBones.rightHand,
        leftThigh: foundBones.leftThigh,
        rightThigh: foundBones.rightThigh,
        leftLeg: foundBones.leftLeg,
        rightLeg: foundBones.rightLeg,
        leftFoot: foundBones.leftFoot,
        rightFoot: foundBones.rightFoot,
      };

      const availableBones = Object.keys(bonesRef.current).filter(key => bonesRef.current[key as keyof typeof bonesRef.current]);
      console.log('✅ Bones stored in ref:', availableBones);
      console.log('📊 Total bones available for animation:', availableBones.length);

      // Store head bone for mouse tracking
      if (foundBones.head && headRef.current) {
        (headRef.current as any).headBone = foundBones.head;
        console.log('🎯 Head bone successfully linked for mouse tracking');
      } else {
        console.log('⚠️ No head bone found in your model, using fallback tracking');
      }

    console.log('🎮 Found bones:', Object.keys(foundBones));
    bonesRef.current = foundBones;

    // PRESERVE ALL BONES IN THEIR ORIGINAL MODEL STATE - NO MODIFICATIONS
    Object.entries(foundBones).forEach(([boneName, bone]: [string, any]) => {
      if (bone) {
        console.log(`📍 ${boneName} - preserved in original model state`);
      }
    });
  }, [fbx]);

  // Enhanced LARGE fallback model if FBX not loaded
  if (!fbx) {
    console.log('🔄 FBX not loaded, showing LARGE enhanced geometric robot');
    return (
      <group ref={modelRef} position={[0, -2, 0]} scale={scale * 1.5}> {/* Extra large scale */}
        {/* Large Torso */}
        <mesh position={[0, 0, 0]}>
          <boxGeometry args={[2.0, 3.0, 1.0]} />
          <meshStandardMaterial
            color="#00ff9e"
            metalness={0.8}
            roughness={0.2}
            emissive="#003d2e"
            emissiveIntensity={0.1}
          />
        </mesh>

        {/* Large Head with mouse tracking */}
        <group ref={headRef} position={[0, 2.0, 0]}>
          <mesh>
            <sphereGeometry args={[0.7]} />
            <meshStandardMaterial
              color="#ffffff"
              metalness={0.9}
              roughness={0.1}
              emissive="#00ff9e"
              emissiveIntensity={0.2}
            />
          </mesh>
          {/* Large Eyes */}
          <mesh position={[-0.25, 0.15, 0.5]}>
            <sphereGeometry args={[0.12]} />
            <meshStandardMaterial color="#0066ff" emissive="#0066ff" emissiveIntensity={0.8} />
          </mesh>
          <mesh position={[0.25, 0.15, 0.5]}>
            <sphereGeometry args={[0.12]} />
            <meshStandardMaterial color="#0066ff" emissive="#0066ff" emissiveIntensity={0.8} />
          </mesh>
          {/* Antenna */}
          <mesh position={[0, 0.6, 0]}>
            <cylinderGeometry args={[0.05, 0.05, 0.4]} />
            <meshStandardMaterial color="#ff6b6b" emissive="#ff6b6b" emissiveIntensity={0.5} />
          </mesh>
        </group>

        {/* Large Arms with joints */}
        <group position={[-1.5, 0.5, 0]}>
          <mesh position={[0, 0, 0]}>
            <boxGeometry args={[0.4, 2.2, 0.4]} />
            <meshStandardMaterial color="#00ff9e" metalness={0.7} roughness={0.3} />
          </mesh>
          {/* Left Hand */}
          <mesh position={[0, -1.3, 0]}>
            <sphereGeometry args={[0.3]} />
            <meshStandardMaterial color="#ffffff" metalness={0.8} roughness={0.2} />
          </mesh>
        </group>

        <group position={[1.5, 0.5, 0]}>
          <mesh position={[0, 0, 0]}>
            <boxGeometry args={[0.4, 2.2, 0.4]} />
            <meshStandardMaterial color="#00ff9e" metalness={0.7} roughness={0.3} />
          </mesh>
          {/* Right Hand */}
          <mesh position={[0, -1.3, 0]}>
            <sphereGeometry args={[0.3]} />
            <meshStandardMaterial color="#ffffff" metalness={0.8} roughness={0.2} />
          </mesh>
        </group>

        {/* Large Legs */}
        <mesh position={[-0.6, -2.0, 0]}>
          <boxGeometry args={[0.5, 2.8, 0.5]} />
          <meshStandardMaterial color="#00ff9e" metalness={0.7} roughness={0.3} />
        </mesh>
        <mesh position={[0.6, -2.0, 0]}>
          <boxGeometry args={[0.5, 2.8, 0.5]} />
          <meshStandardMaterial color="#00ff9e" metalness={0.7} roughness={0.3} />
        </mesh>

        {/* Large Chest detail */}
        <mesh position={[0, 0.5, 0.51]}>
          <boxGeometry args={[1.0, 1.0, 0.2]} />
          <meshStandardMaterial
            color="#ffffff"
            emissive="#00ff9e"
            emissiveIntensity={0.4}
            metalness={0.9}
            roughness={0.1}
          />
        </mesh>

        {/* Shoulder pads */}
        <mesh position={[-1.2, 1.2, 0]}>
          <sphereGeometry args={[0.4]} />
          <meshStandardMaterial color="#ff6b6b" metalness={0.8} roughness={0.2} />
        </mesh>
        <mesh position={[1.2, 1.2, 0]}>
          <sphereGeometry args={[0.4]} />
          <meshStandardMaterial color="#ff6b6b" metalness={0.8} roughness={0.2} />
        </mesh>
      </group>
    );
  }

  console.log('🤖 Rendering actual robot model with advanced animations');
  console.log('📏 Model scale:', scale, 'Section:', activeSection);
  console.log('🔍 FBX object:', fbx);
  console.log('🔍 FBX children count:', fbx.children.length);

  return (
    <group ref={modelRef} position={[0, -1, 0]} scale={scale}>
      <primitive object={fbx} />

      {/* Debug: Add a visible indicator that the model is there */}
      <mesh position={[0, 3, 0]} visible={true}>
        <sphereGeometry args={[0.2]} />
        <meshBasicMaterial color="#ff0000" />
      </mesh>

      {/* Debug: Add text to show current section */}
      <mesh position={[0, 4, 0]}>
        <boxGeometry args={[0.1, 0.1, 0.1]} />
        <meshBasicMaterial color="#00ff00" />
      </mesh>
    </group>
  );
}

// Enhanced scene component with context awareness
interface ModelSceneProps {
  activeSection?: string;
  className?: string;
}

const ModelScene: React.FC<ModelSceneProps> = ({
  activeSection = 'hero',
  className = "absolute inset-0 z-0"
}) => {
  const [mousePosition, setMousePosition] = useState({ x: 0.5, y: 0.5 });
  const [modelScale, setModelScale] = useState(1.5);

  console.log('ModelScene rendering for section:', activeSection);

  // Mouse tracking
  useEffect(() => {
    const handleMouseMove = (event: MouseEvent) => {
      const x = event.clientX / window.innerWidth;
      const y = event.clientY / window.innerHeight;
      setMousePosition({ x, y });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Determine scale based on section - MUCH LARGER SCALES
  useEffect(() => {
    const sectionScales = {
      hero: 4.0,      // Much larger
      about: 3.5,     // Much larger
      skills: 4.5,    // Much larger
      experience: 3.2, // Much larger
      projects: 5.0,  // Much larger
      contact: 3.8    // Much larger
    };

    const scale = sectionScales[activeSection as keyof typeof sectionScales] || 3.0;
    setModelScale(scale);
  }, [activeSection]);

  // Dynamic lighting based on section
  const getLighting = () => {
    switch (activeSection) {
      case 'hero':
        return {
          ambient: { intensity: 0.6, color: "#ffffff" },
          directional: { intensity: 1.8, color: "#00ff9e", position: [5, 5, 5] },
          point: { intensity: 1.2, color: "#00ff9e", position: [0, 0, 5] }
        };
      case 'about':
        return {
          ambient: { intensity: 0.8, color: "#ffffff" },
          directional: { intensity: 1.5, color: "#ffffff", position: [-5, 5, 5] },
          point: { intensity: 0.8, color: "#4f46e5", position: [-2, 2, 3] }
        };
      case 'skills':
        return {
          ambient: { intensity: 0.7, color: "#ffffff" },
          directional: { intensity: 2.0, color: "#00ff9e", position: [5, 5, 5] },
          point: { intensity: 1.5, color: "#ff6b6b", position: [2, 0, 5] }
        };
      case 'projects':
        return {
          ambient: { intensity: 0.9, color: "#ffffff" },
          directional: { intensity: 1.6, color: "#ffffff", position: [5, 5, 5] },
          point: { intensity: 1.0, color: "#ffd93d", position: [0, 3, 4] }
        };
      case 'contact':
        return {
          ambient: { intensity: 1.0, color: "#ffffff" },
          directional: { intensity: 1.4, color: "#00ff9e", position: [0, 5, 5] },
          point: { intensity: 1.3, color: "#00ff9e", position: [0, 0, 6] }
        };
      default:
        return {
          ambient: { intensity: 0.8, color: "#ffffff" },
          directional: { intensity: 1.5, color: "#ffffff", position: [5, 5, 5] },
          point: { intensity: 1.0, color: "#00ff9e", position: [0, 0, 5] }
        };
    }
  };

  const lighting = getLighting();

  return (
    <div className={className}>
      <Canvas
        camera={{ position: [0, 2, 8], fov: 75 }}
        gl={{
          antialias: true,
          alpha: true,
          powerPreference: "high-performance"
        }}
        onCreated={({ gl }) => {
          console.log('🎬 Centralized Canvas created for section:', activeSection);
          gl.setClearColor(0x000000, 0);
        }}
      >
        <ambientLight
          intensity={lighting.ambient.intensity}
          color={lighting.ambient.color}
        />
        <directionalLight
          position={lighting.directional.position as [number, number, number]}
          intensity={lighting.directional.intensity}
          color={lighting.directional.color}
          castShadow
        />
        <pointLight
          position={lighting.point.position as [number, number, number]}
          intensity={lighting.point.intensity}
          color={lighting.point.color}
        />

        {/* Additional accent lighting */}
        <spotLight
          position={[0, 10, 0]}
          angle={0.3}
          penumbra={1}
          intensity={0.5}
          color="#00ff9e"
          target-position={[0, 0, 0]}
        />

        <RobotModel
          activeSection={activeSection}
          mousePosition={mousePosition}
          scale={modelScale}
        />
      </Canvas>
    </div>
  );
};

// Preload your actual FBX model
try {
  useFBX.preload('/models/sci-fi-robot.fbx');
  console.log('Preloading your sci-fi-robot.fbx model...');
} catch (error) {
  console.log('FBX preload failed, model will load on demand');
}

export default ModelScene;