import { useRef, useState, useEffect } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { useGLTF, Environment } from '@react-three/drei';
import * as THREE from 'three';
import { useScroll } from 'framer-motion';

interface RobotModelProps {
  scrollYProgress: any;
  mousePosition: { x: number; y: number };
}

// Robot model component
function RobotModel({ scrollYProgress, mousePosition }: RobotModelProps) {
  const { scene, nodes, materials, animations } = useGLTF('/models/sci-fi-robot.glb');

  const modelRef = useRef<THREE.Group>(null);
  const [animationState, setAnimationState] = useState({
    waving: false,
    pointing: false,
    headTracking: true
  });
  
  // Clone the scene to avoid modifying the cached original
  useEffect(() => {
    if (scene) {
      console.log('Model loaded successfully:', { scene, nodes, materials, animations });
    }
  }, [scene, nodes, materials, animations]);

  // Handle scroll-based animations
  useFrame((state) => {
    if (!modelRef.current) return;

    // Smooth rotation based on scroll
    const targetRotationY = THREE.MathUtils.lerp(
      -Math.PI / 6, // Starting rotation
      Math.PI / 6,  // Ending rotation
      scrollYProgress.get()
    );

    modelRef.current.rotation.y = THREE.MathUtils.lerp(
      modelRef.current.rotation.y,
      targetRotationY,
      0.05
    );

    // Subtle floating animation
    modelRef.current.position.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.05 + 0.05;
    
    // Head tracking for mouse position
    if (animationState.headTracking && nodes && (nodes as any).Head) {
      // Find the head bone/node
      const headBone = modelRef.current.getObjectByName('Head') ||
                      modelRef.current.getObjectByName('head') ||
                      modelRef.current.getObjectByName('mixamorigHead');

      if (headBone) {
        // Calculate target rotation based on mouse position
        const targetRotationX = THREE.MathUtils.lerp(
          -0.2, // Max left
          0.2,  // Max right
          mousePosition.x
        );

        const targetRotationY = THREE.MathUtils.lerp(
          0.2,  // Max down
          -0.2, // Max up
          mousePosition.y
        );

        // Apply smooth rotation to head
        headBone.rotation.x = THREE.MathUtils.lerp(
          headBone.rotation.x,
          targetRotationY,
          0.05
        );

        headBone.rotation.y = THREE.MathUtils.lerp(
          headBone.rotation.y,
          targetRotationX,
          0.05
        );
      }
    }
  });
  
  // Trigger waving animation when mouse is in top-right corner
  useEffect(() => {
    if (mousePosition.x > 0.8 && mousePosition.y < 0.2) {
      if (!animationState.waving) {
        setAnimationState(prev => ({ ...prev, waving: true }));
        
        // Reset after animation
        setTimeout(() => {
          setAnimationState(prev => ({ ...prev, waving: false }));
        }, 2000);
      }
    }
  }, [mousePosition, animationState.waving]);
  
  // Apply waving animation
  useEffect(() => {
    if (!modelRef.current) return;

    const rightArm = modelRef.current.getObjectByName('RightArm') ||
                    modelRef.current.getObjectByName('right_arm') ||
                    modelRef.current.getObjectByName('mixamorigRightArm');

    if (rightArm && animationState.waving) {
      // Simple waving animation
      const waveAnimation = (time: number) => {
        (rightArm as THREE.Object3D).rotation.z = Math.sin(time * 10) * 0.3;

        if (animationState.waving) {
          requestAnimationFrame(waveAnimation);
        } else {
          // Reset rotation when animation ends
          (rightArm as THREE.Object3D).rotation.z = 0;
        }
      };

      requestAnimationFrame(waveAnimation);
    }
  }, [animationState.waving]);
  
  return (
    <group ref={modelRef} position={[0, -1, 0]} scale={0.8}>
      <primitive object={scene} />
    </group>
  );
}

// Main scene component
const ModelScene = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0.5, y: 0.5 });
  const { scrollYProgress } = useScroll();
  
  // Track mouse position
  useEffect(() => {
    const handleMouseMove = (event: MouseEvent) => {
      setMousePosition({
        x: event.clientX / window.innerWidth,
        y: event.clientY / window.innerHeight
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);
  
  return (
    <div className="absolute inset-0 z-0">
      <Canvas
        camera={{ position: [0, 0, 5], fov: 45 }}
        shadows
        gl={{ antialias: true, alpha: true }}
      >
        <ambientLight intensity={0.5} />
        <spotLight 
          position={[5, 10, 7.5]} 
          angle={0.15} 
          penumbra={1} 
          intensity={1} 
          castShadow 
          shadow-mapSize={[2048, 2048]}
        />
        <spotLight 
          position={[-5, 10, 7.5]} 
          angle={0.15} 
          penumbra={1} 
          intensity={0.5} 
          color="#00ff9e"
        />
        <RobotModel scrollYProgress={scrollYProgress} mousePosition={mousePosition} />
        <Environment preset="city" />
      </Canvas>
    </div>
  );
};

export default ModelScene;