import { useRef } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { useGLTF } from '@react-three/drei';
import * as THREE from 'three';
import { useScroll } from 'framer-motion';

// Simple robot model component
function RobotModel() {
  const modelRef = useRef<THREE.Group>(null);
  const { scrollYProgress } = useScroll();

  let gltf;
  try {
    gltf = useGLTF('/models/sci-fi-robot.glb');
    console.log('GLTF loaded:', gltf);
  } catch (error) {
    console.error('Error loading GLTF:', error);
  }

  // Simple scroll-based animation
  useFrame((state) => {
    if (!modelRef.current) return;

    const scrollProgress = scrollYProgress.get();
    const time = state.clock.elapsedTime;

    // Rotate based on scroll
    modelRef.current.rotation.y = scrollProgress * Math.PI * 0.5;

    // Float animation
    modelRef.current.position.y = -1 + Math.sin(time) * 0.1;

    // Scale slightly based on scroll
    const scale = 1 + scrollProgress * 0.2;
    modelRef.current.scale.setScalar(scale);
  });

  // Show actual model if loaded, fallback if not
  if (!gltf?.scene) {
    console.log('GLTF not loaded, showing fallback');
    return (
      <group ref={modelRef} position={[0, 0, 0]} scale={2}>
        <mesh position={[0, 0, 0]}>
          <boxGeometry args={[1, 2, 0.5]} />
          <meshStandardMaterial color="#00ff9e" />
        </mesh>
        <mesh position={[0, 1.2, 0]}>
          <sphereGeometry args={[0.3]} />
          <meshStandardMaterial color="#ff0000" />
        </mesh>
      </group>
    );
  }

  console.log('Rendering actual robot model');
  return (
    <group ref={modelRef} position={[0, -1, 0]} scale={1.5}>
      <primitive object={gltf.scene} />
    </group>
  );
}

// Main scene component
const ModelScene = () => {
  console.log('ModelScene rendering');

  return (
    <div className="absolute inset-0 z-0">
      <Canvas
        camera={{ position: [3, 2, 5], fov: 75 }}
        gl={{
          antialias: true,
          alpha: true,
          powerPreference: "high-performance"
        }}
        onCreated={({ gl }) => {
          console.log('Canvas created');
          gl.setClearColor(0x000000, 0);
        }}
      >
        <ambientLight intensity={0.8} color="#ffffff" />
        <directionalLight
          position={[5, 5, 5]}
          intensity={1.5}
          color="#ffffff"
        />
        <pointLight
          position={[0, 0, 5]}
          intensity={1}
          color="#00ff9e"
        />

        <RobotModel />
      </Canvas>
    </div>
  );
};

// Preload the model
useGLTF.preload('/models/sci-fi-robot.glb');

export default ModelScene;