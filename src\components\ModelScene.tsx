import { useRef, useState, useEffect } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { useGLTF, Environment } from '@react-three/drei';
import * as THREE from 'three';

function RobotModel({ mousePos, scrollYProgress }: { mousePos: { x: number; y: number }; scrollYProgress: number }) {
  const modelRef = useRef<THREE.Group>(null);
  const [modelLoaded, setModelLoaded] = useState(false);

  // Load GLB model with error handling
  let model;
  try {
    const gltf = useGLTF('/models/sci-fi-robot.glb');
    model = gltf.scene;
  } catch (error) {
    console.error('Error loading GLTF model:', error);
    model = null;
  }

  useEffect(() => {
    console.log('Model loading state:', {
      hasModel: !!model,
      modelLoaded,
      modelType: model?.type,
      modelChildren: model?.children?.length
    });
  }, [model, modelLoaded]);

  useEffect(() => {
    if (model && modelRef.current) {
      // Clone the model to avoid modifying the cached version
      const clonedModel = model.clone();

      // Clear any existing children
      while (modelRef.current.children.length > 0) {
        modelRef.current.remove(modelRef.current.children[0]);
      }

      // Add the cloned model
      modelRef.current.add(clonedModel);

      // Set initial transform
      modelRef.current.scale.setScalar(1.5);
      modelRef.current.position.set(0, -1.5, 0);
      modelRef.current.rotation.set(0, Math.PI * 0.15, 0);

      // Configure materials and shadows
      clonedModel.traverse((child: any) => {
        if (child.isMesh) {
          child.castShadow = true;
          child.receiveShadow = true;
          if (child.material) {
            child.material.needsUpdate = true;
            // Ensure materials are visible
            if (child.material.transparent) {
              child.material.opacity = Math.max(child.material.opacity, 0.8);
            }
          }
        }
      });

      setModelLoaded(true);
      console.log('Model loaded and configured successfully');
    }
  }, [model]);

  useFrame((state) => {
    if (!modelRef.current || !modelLoaded) return;

    const time = state.clock.getElapsedTime();

    // Smooth mouse following with limits
    const targetRotationY = Math.PI * 0.15 + THREE.MathUtils.lerp(
      -Math.PI / 6,
      Math.PI / 6,
      (mousePos.x + 1) / 2
    );
    const targetRotationX = THREE.MathUtils.lerp(
      -Math.PI / 12,
      Math.PI / 12,
      (mousePos.y + 1) / 2
    );

    // Apply smooth rotation
    modelRef.current.rotation.y = THREE.MathUtils.lerp(
      modelRef.current.rotation.y,
      targetRotationY,
      0.03
    );
    modelRef.current.rotation.x = THREE.MathUtils.lerp(
      modelRef.current.rotation.x,
      targetRotationX,
      0.03
    );

    // Breathing animation
    modelRef.current.position.y = -1.5 + Math.sin(time * 0.8) * 0.08;

    // Scroll-based scale and position
    const baseScale = 1.5;
    const scrollScale = 1 + scrollYProgress * 0.3;
    modelRef.current.scale.setScalar(baseScale * scrollScale);

    // Subtle scroll-based rotation
    const scrollRotation = scrollYProgress * Math.PI * 0.1;
    modelRef.current.rotation.z = Math.sin(scrollRotation) * 0.05;
  });

  // Show a fallback if model isn't loaded yet
  if (!modelLoaded || !model) {
    return (
      <group>
        <mesh position={[0, 0, 0]}>
          <boxGeometry args={[0.8, 1.6, 0.4]} />
          <meshStandardMaterial color="#00ff9e" />
        </mesh>
        <mesh position={[0, 0.9, 0]}>
          <sphereGeometry args={[0.25]} />
          <meshStandardMaterial color="#00ff9e" />
        </mesh>
        {/* Arms */}
        <mesh position={[-0.6, 0.3, 0]}>
          <boxGeometry args={[0.2, 0.8, 0.2]} />
          <meshStandardMaterial color="#00ff9e" />
        </mesh>
        <mesh position={[0.6, 0.3, 0]}>
          <boxGeometry args={[0.2, 0.8, 0.2]} />
          <meshStandardMaterial color="#00ff9e" />
        </mesh>
        {/* Legs */}
        <mesh position={[-0.3, -1.2, 0]}>
          <boxGeometry args={[0.25, 1.0, 0.25]} />
          <meshStandardMaterial color="#00ff9e" />
        </mesh>
        <mesh position={[0.3, -1.2, 0]}>
          <boxGeometry args={[0.25, 1.0, 0.25]} />
          <meshStandardMaterial color="#00ff9e" />
        </mesh>
      </group>
    );
  }

  return <group ref={modelRef} />;
}

export default function ModelScene() {
  const [mousePos, setMousePos] = useState({ x: 0, y: 0 });
  const [scrollYProgress, setScrollYProgress] = useState(0);

  // Track scroll position with better calculation
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollPercent = docHeight > 0 ? scrollTop / docHeight : 0;
      setScrollYProgress(Math.min(Math.max(scrollPercent, 0), 1));
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll(); // Initial call

    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Track mouse position relative to the canvas
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePos({
        x: (e.clientX / window.innerWidth) * 2 - 1,
        y: -(e.clientY / window.innerHeight) * 2 + 1,
      });
    };

    window.addEventListener('mousemove', handleMouseMove, { passive: true });

    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return (
    <div className="w-full h-full">
      <Canvas
        shadows
        dpr={[1, 2]}
        camera={{ position: [0, 0, 5], fov: 45 }}
        gl={{ antialias: true, alpha: true }}
      >
        {/* Improved lighting setup */}
        <ambientLight intensity={0.4} />
        <directionalLight
          position={[10, 10, 5]}
          intensity={1.2}
          castShadow
          shadow-mapSize={[2048, 2048]}
          shadow-camera-far={50}
          shadow-camera-left={-10}
          shadow-camera-right={10}
          shadow-camera-top={10}
          shadow-camera-bottom={-10}
        />
        <pointLight position={[-10, 5, -5]} intensity={0.6} color="#00ff9e" />
        <spotLight
          position={[0, 10, 0]}
          angle={0.3}
          penumbra={1}
          intensity={0.8}
          color="#ffffff"
        />

        <RobotModel mousePos={mousePos} scrollYProgress={scrollYProgress} />
        <Environment preset="city" />
      </Canvas>
    </div>
  );
}

// Preload the model
useGLTF.preload('/models/sci-fi-robot.glb');