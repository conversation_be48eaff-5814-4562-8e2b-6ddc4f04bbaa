import { useRef, useState, useEffect } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { useFBX, Environment } from '@react-three/drei';
import * as THREE from 'three';

interface RobotModelProps {
  mousePos: { x: number; y: number };
  scrollYProgress: number;
}

function RobotModel({ mousePos, scrollYProgress }: RobotModelProps) {
  const groupRef = useRef<THREE.Group>(null);
  const mixerRef = useRef<THREE.AnimationMixer | null>(null);
  const [modelReady, setModelReady] = useState(false);
  const [animationActions, setAnimationActions] = useState<THREE.AnimationAction[]>([]);

  // Load FBX model
  const fbx = useFBX('/models/sci-fi-robot.fbx');

  // Initialize model when FBX loads
  useEffect(() => {
    if (fbx && groupRef.current) {
      console.log('FBX Model loaded:', fbx);

      // Clear any existing children
      while (groupRef.current.children.length > 0) {
        groupRef.current.remove(groupRef.current.children[0]);
      }

      // Clone the FBX to avoid modifying the cached version
      const clonedFBX = fbx.clone();

      // Set up the model
      clonedFBX.scale.setScalar(0.01); // FBX models are often very large
      clonedFBX.position.set(0, -2, 0);
      clonedFBX.rotation.set(0, 0, 0);

      // Configure materials and shadows
      clonedFBX.traverse((child: any) => {
        if (child.isMesh) {
          child.castShadow = true;
          child.receiveShadow = true;

          // Ensure materials are visible
          if (child.material) {
            if (Array.isArray(child.material)) {
              child.material.forEach((mat: any) => {
                mat.needsUpdate = true;
                if (mat.map) mat.map.needsUpdate = true;
              });
            } else {
              child.material.needsUpdate = true;
              if (child.material.map) child.material.map.needsUpdate = true;
            }
          }
        }
      });

      // Add to group
      groupRef.current.add(clonedFBX);

      // Set up animation mixer if animations exist
      if (fbx.animations && fbx.animations.length > 0) {
        const mixer = new THREE.AnimationMixer(clonedFBX);
        mixerRef.current = mixer;

        const actions = fbx.animations.map((clip) => {
          const action = mixer.clipAction(clip);
          action.play();
          return action;
        });

        setAnimationActions(actions);
        console.log(`Loaded ${fbx.animations.length} animations`);
      }

      setModelReady(true);
      console.log('Model setup complete');
    }
  }, [fbx]);

  // Animation loop
  useFrame((state, delta) => {
    if (!groupRef.current || !modelReady) return;

    // Update animation mixer
    if (mixerRef.current) {
      mixerRef.current.update(delta);
    }

    const time = state.clock.getElapsedTime();

    // Mouse-based rotation
    const targetRotationY = THREE.MathUtils.lerp(
      -Math.PI / 6,
      Math.PI / 6,
      (mousePos.x + 1) / 2
    );

    const targetRotationX = THREE.MathUtils.lerp(
      -Math.PI / 12,
      Math.PI / 12,
      (mousePos.y + 1) / 2
    );

    // Apply smooth rotation
    groupRef.current.rotation.y = THREE.MathUtils.lerp(
      groupRef.current.rotation.y,
      targetRotationY,
      0.02
    );

    groupRef.current.rotation.x = THREE.MathUtils.lerp(
      groupRef.current.rotation.x,
      targetRotationX,
      0.02
    );

    // Floating animation
    groupRef.current.position.y = -2 + Math.sin(time * 0.6) * 0.1;

    // Scroll-based effects
    const scrollScale = 1 + scrollYProgress * 0.5;
    groupRef.current.scale.setScalar(0.01 * scrollScale);

    // Subtle scroll-based rotation
    const scrollRotation = scrollYProgress * Math.PI * 0.2;
    groupRef.current.rotation.z = Math.sin(scrollRotation) * 0.05;
  });

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (mixerRef.current) {
        mixerRef.current.stopAllAction();
        mixerRef.current = null;
      }
    };
  }, []);

  // Render fallback if model not ready
  if (!modelReady) {
    return (
      <group ref={groupRef}>
        <mesh position={[0, 0, 0]}>
          <boxGeometry args={[0.8, 1.6, 0.4]} />
          <meshStandardMaterial color="#00ff9e" />
        </mesh>
        <mesh position={[0, 0.9, 0]}>
          <sphereGeometry args={[0.25]} />
          <meshStandardMaterial color="#00ff9e" />
        </mesh>
        {/* Arms */}
        <mesh position={[-0.6, 0.3, 0]}>
          <boxGeometry args={[0.2, 0.8, 0.2]} />
          <meshStandardMaterial color="#00ff9e" />
        </mesh>
        <mesh position={[0.6, 0.3, 0]}>
          <boxGeometry args={[0.2, 0.8, 0.2]} />
          <meshStandardMaterial color="#00ff9e" />
        </mesh>
        {/* Legs */}
        <mesh position={[-0.3, -1.2, 0]}>
          <boxGeometry args={[0.25, 1.0, 0.25]} />
          <meshStandardMaterial color="#00ff9e" />
        </mesh>
        <mesh position={[0.3, -1.2, 0]}>
          <boxGeometry args={[0.25, 1.0, 0.25]} />
          <meshStandardMaterial color="#00ff9e" />
        </mesh>
      </group>
    );
  }

  return <group ref={groupRef} />;
}

export default function ModelScene() {
  const [mousePos, setMousePos] = useState({ x: 0, y: 0 });
  const [scrollYProgress, setScrollYProgress] = useState(0);

  // Track scroll position
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollPercent = docHeight > 0 ? scrollTop / docHeight : 0;
      setScrollYProgress(Math.min(Math.max(scrollPercent, 0), 1));
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll();

    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Track mouse position
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePos({
        x: (e.clientX / window.innerWidth) * 2 - 1,
        y: -(e.clientY / window.innerHeight) * 2 + 1,
      });
    };

    window.addEventListener('mousemove', handleMouseMove, { passive: true });

    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return (
    <div className="w-full h-full">
      <Canvas
        shadows
        dpr={[1, 2]}
        camera={{ position: [2, 1, 4], fov: 50 }}
        gl={{ antialias: true, alpha: true }}
      >
        {/* Lighting setup optimized for FBX models */}
        <ambientLight intensity={0.6} />
        <directionalLight
          position={[5, 10, 5]}
          intensity={1.5}
          castShadow
          shadow-mapSize={[1024, 1024]}
          shadow-camera-far={50}
          shadow-camera-left={-10}
          shadow-camera-right={10}
          shadow-camera-top={10}
          shadow-camera-bottom={-10}
        />
        <pointLight position={[-5, 5, 5]} intensity={0.8} color="#00ff9e" />
        <spotLight
          position={[0, 8, 3]}
          angle={0.4}
          penumbra={1}
          intensity={1}
          color="#ffffff"
        />

        <RobotModel mousePos={mousePos} scrollYProgress={scrollYProgress} />
        <Environment preset="sunset" />
      </Canvas>
    </div>
  );
}

// Preload the FBX model
useFBX.preload('/models/sci-fi-robot.fbx');