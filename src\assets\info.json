{"personal": {"name": "<PERSON><PERSON><PERSON>", "title": "SR. NodeJS Developer", "description": "Experienced in developing high-performance backend systems, specializing in Node.js and related technologies.", "experienceYears": 4, "phone": "+91 123456789", "email": "<EMAIL>", "avatar": "https://example.com/avatar.png", "summary": ["4+ years of experience specializing in building scalable and efficient backend applications ensuring high performance and security.", "Expertise in creating robust APIs and delivering modern solutions to complex challenges.", "Proficient with Node.js frameworks such as Express.js, Koa, and NestJS.", "Strong experience with databases like MySQL, PostgreSQL, and MongoDB.", "Skilled in integrating third-party APIs (OpenAI, Meta, Google APIs).", "Adept at real-time functionalities using WebSockets and similar technologies.", "Hands-on experience deploying and managing applications on cloud platforms such as AWS, Azure, and Hostinger."]}, "skills": {"programmingLanguages": [{"name": "JavaScript", "logo": "https://img.icons8.com/color/48/000000/javascript.png"}, {"name": "TypeScript", "logo": "https://img.icons8.com/color/48/000000/typescript.png"}, {"name": "HTML", "logo": "https://img.icons8.com/color/48/000000/html-5.png"}, {"name": "CSS", "logo": "https://img.icons8.com/color/48/000000/css3.png"}], "databases": [{"name": "MySQL", "logo": "https://img.icons8.com/color/48/000000/mysql.png"}, {"name": "MongoDB", "logo": "https://img.icons8.com/color/48/000000/mongodb.png"}, {"name": "Firebase", "logo": "https://img.icons8.com/color/48/000000/firebase.png"}, {"name": "PostgreSQL", "logo": "https://img.icons8.com/?size=100&id=REf3ZfzC88t5&format=png&color=000000"}], "frameworks": [{"name": "Node.js", "logo": "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nodejs/nodejs-original.svg"}], "tools": [{"name": "VS Code", "logo": "https://img.icons8.com/color/48/000000/visual-studio-code-2019.png"}, {"name": "Git", "logo": "https://img.icons8.com/color/48/000000/git.png"}, {"name": "<PERSON>er", "logo": "https://img.icons8.com/color/48/000000/docker.png"}, {"name": "Kubernetes", "logo": "https://img.icons8.com/color/48/000000/kubernetes.png"}, {"name": "Linux", "logo": "https://img.icons8.com/color/48/000000/linux.png"}, {"name": "Socket.io", "logo": "https://www.vectorlogo.zone/logos/socketio/socketio-icon.svg"}, {"name": "Stripe", "logo": "https://img.icons8.com/color/48/000000/stripe.png"}, {"name": "AWS", "logo": "https://img.icons8.com/color/48/000000/amazon-web-services.png"}]}, "experiences": [{"title": "SR. NodeJS Developer", "company": "Jnext Services Pvt. Ltd.", "duration": "2025 - current", "description": "Leading backend architecture and API development using Node.js, integrating real-time features, managing cloud deployments, and mentoring developers."}, {"title": "FullStack Developer", "company": "Flexion Infotech Pvt. Ltd.", "duration": "2021 - 2024", "description": "Built full-stack web applications using React and Node.js, implemented RESTful APIs, managed databases, and collaborated on UI/UX and CI/CD processes."}], "projects": [{"title": "<PERSON><PERSON>", "position": "Backend Developer", "company": "Jnext Services Pvt. Ltd.", "technologies": ["Node.js", "AWS", "WebSockets"], "image": "https://example.com/oktion.png", "description": "Event management tool enabling fundraising through Tickets, Auctions, Raffles and Donations.", "responsibilities": ["Developed the project from scratch, ensuring optimized backend performance.", "Deployed the application on AWS and maintained server reliability.", "Enhanced API performance by 30%.", "Integrated real-time updates for event status and participation using WebSockets.", "Implemented secure payment gateways and ticketing functionalities.", "Conducted regular code reviews to maintain code quality and performance."]}, {"title": "LDMS - Legal Document Management System", "position": "Backend Developer", "company": "Jnext Services Pvt. Ltd.", "technologies": ["Node.js", "MFA", "Role-based access"], "image": "https://example.com/ldms.png", "description": "Platform for lawyers to securely manage, collaborate and track legal documents.", "responsibilities": ["Ensured secure document sharing and editing based on role-based permissions.", "Designed a detailed activity log for document edits and access.", "Implemented multi-factor authentication for secure access.", "Developed a robust version control system for document updates.", "Coordinated with legal teams to align features with industry requirements."]}, {"title": "<PERSON><PERSON><PERSON>", "position": "Backend Developer", "company": "Jnext Services Pvt. Ltd.", "technologies": ["Node.js", "Stripe", "Socket.io"], "image": "https://example.com/puppi-lovers.png", "description": "A dating app for dog owners with verified profiles and video verification, proceeds support animal charities.", "responsibilities": ["Developed scalable backend APIs for authentication, matchmaking, and messaging.", "Built an admin panel using AdminBro.", "Implemented video-based verification and role-based access controls.", "Integrated Stripe for subscription management.", "Designed and maintained activity logs.", "Collaborated with cross-functional teams aligning backend with UX and marketing."]}, {"title": "Sezz You", "position": "Backend Developer", "company": "Jnext Services Pvt. Ltd.", "technologies": ["Node.js", "WebSockets", "Push Notifications"], "image": "https://example.com/sezz-you.png", "description": "Social discussion platform with media-rich posts, polls, and moderation tools.", "responsibilities": ["Built RESTful APIs for topics, commenting and polling.", "Integrated real-time chat functionality using WebSockets.", "Implemented demographic-based poll filtering.", "Developed secure phone number verification.", "Integrated push notifications.", "Optimized content delivery based on user interests."]}, {"title": "Micro Office", "position": "Backend Developer", "company": "Jnext Services Pvt. Ltd.", "technologies": ["Node.js", "<PERSON>er", "Kubernetes"], "image": "https://example.com/micro-office.png", "description": "SaaS platform for HR and staff management, shift scheduling, and compliance across global locations.", "responsibilities": ["Developed APIs for staff profiles, onboarding workflows, and scheduling.", "Designed and deployed microservices with Docker and Kubernetes.", "Implemented secure document management with versioning.", "Designed role-based access controls.", "Built notification systems for scheduling and reminders."]}], "contact": {"form": true, "social": {"linkedin": "https://linkedin.com/in/dishangpatel", "github": "https://github.com/dishangpatel"}}}