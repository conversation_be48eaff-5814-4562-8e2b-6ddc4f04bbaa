import { useEffect, useRef, useState } from 'react';
import * as THREE from 'three';

interface ParticleBackgroundProps {
  enabled: boolean;
}

const ParticleBackground: React.FC<ParticleBackgroundProps> = ({ enabled }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const mousePosition = useRef({ x: 0, y: 0 });
  
  // If reduced motion is enabled, don't show particle background
  if (!enabled) {
    return <div className="fixed inset-0 bg-[#0a0f1d] z-0" />;
  }
  
  useEffect(() => {
    if (!containerRef.current) return;
    
    // Set up Three.js scene
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    camera.position.z = 20;
    
    // Create WebGL renderer
    const renderer = new THREE.WebGLRenderer({ alpha: true, antialias: true });
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    containerRef.current.appendChild(renderer.domElement);
    
    // Create particles
    const particleCount = Math.min(Math.floor(window.innerWidth * 0.05), 250); // Responsive particle count
    
    // Particle class to manage individual particles
    class Particle {
      position: THREE.Vector3;
      originalPosition: THREE.Vector3;
      velocity: THREE.Vector3;
      size: number;
      color: THREE.Color;
      
      constructor() {
        // Create a grid-like distribution with some randomness
        const gridSize = Math.ceil(Math.sqrt(particleCount));
        const cellSize = 30 / gridSize;
        const index = Math.floor(Math.random() * particleCount);
        const col = index % gridSize;
        const row = Math.floor(index / gridSize);
        
        const x = (col * cellSize) - 15 + (Math.random() * cellSize * 0.8);
        const y = (row * cellSize) - 15 + (Math.random() * cellSize * 0.8);
        const z = (Math.random() - 0.5) * 2;
        
        this.position = new THREE.Vector3(x, y, z);
        this.originalPosition = this.position.clone();
        this.velocity = new THREE.Vector3(
          (Math.random() - 0.5) * 0.01,
          (Math.random() - 0.5) * 0.01,
          0
        );
        this.size = Math.random() * 0.1 + 0.05;
        
        // Color - blue to cyan range
        const hue = 0.6 + Math.random() * 0.1; // Blue to cyan (0.6 - 0.7)
        const saturation = 0.7 + Math.random() * 0.3;
        const lightness = 0.5 + Math.random() * 0.2;
        
        this.color = new THREE.Color().setHSL(hue, saturation, lightness);
      }
      
      update(mousePosition: { x: number, y: number }, delta: number) {
        // Convert normalized mouse position to world coordinates
        const mouseX = mousePosition.x * 20;
        const mouseY = mousePosition.y * 20;
        const mouseVector = new THREE.Vector3(mouseX, mouseY, 0);
        
        // Calculate distance to mouse
        const distanceToMouse = this.position.distanceTo(mouseVector);
        
        // Apply attraction to mouse if close enough
        if (distanceToMouse < 5) {
          const attraction = new THREE.Vector3()
            .subVectors(mouseVector, this.position)
            .normalize()
            .multiplyScalar(0.03 * (1 - distanceToMouse / 5));
          
          this.velocity.add(attraction);
        }
        
        // Apply spring force back to original position
        const springForce = new THREE.Vector3()
          .subVectors(this.originalPosition, this.position)
          .multiplyScalar(0.01);
        
        this.velocity.add(springForce);
        
        // Apply damping
        this.velocity.multiplyScalar(0.95);
        
        // Update position
        this.position.add(this.velocity);
      }
    }
    
    // Create particles
    const particles: Particle[] = [];
    for (let i = 0; i < particleCount; i++) {
      particles.push(new Particle());
    }
    
    // Create geometry for particles
    const particleGeometry = new THREE.BufferGeometry();
    const positions = new Float32Array(particleCount * 3);
    const colors = new Float32Array(particleCount * 3);
    const sizes = new Float32Array(particleCount);
    
    // Set initial positions, colors, and sizes
    for (let i = 0; i < particleCount; i++) {
      const particle = particles[i];
      positions[i * 3] = particle.position.x;
      positions[i * 3 + 1] = particle.position.y;
      positions[i * 3 + 2] = particle.position.z;
      
      colors[i * 3] = particle.color.r;
      colors[i * 3 + 1] = particle.color.g;
      colors[i * 3 + 2] = particle.color.b;
      
      sizes[i] = particle.size;
    }
    
    particleGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    particleGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
    particleGeometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));
    
    // Create shader material for particles
    const particleMaterial = new THREE.ShaderMaterial({
      vertexShader: `
        attribute float size;
        attribute vec3 color;
        varying vec3 vColor;
        void main() {
          vColor = color;
          vec4 mvPosition = modelViewMatrix * vec4(position, 1.0);
          gl_PointSize = size * (300.0 / -mvPosition.z);
          gl_Position = projectionMatrix * mvPosition;
        }
      `,
      fragmentShader: `
        varying vec3 vColor;
        void main() {
          float distance = length(gl_PointCoord - vec2(0.5, 0.5));
          if (distance > 0.5) discard;
          float alpha = 1.0 - smoothstep(0.4, 0.5, distance);
          gl_FragColor = vec4(vColor, alpha);
        }
      `,
      transparent: true,
      blending: THREE.AdditiveBlending,
      depthWrite: false
    });
    
    // Create particle system
    const particleSystem = new THREE.Points(particleGeometry, particleMaterial);
    scene.add(particleSystem);
    
    // Create lines between particles
    const linesMaterial = new THREE.LineBasicMaterial({
      color: 0x4a5568,
      transparent: true,
      opacity: 0.2,
      blending: THREE.AdditiveBlending
    });
    
    const linesGeometry = new THREE.BufferGeometry();
    const linesGroup = new THREE.Group();
    scene.add(linesGroup);
    
    // Handle mouse movement
    const handleMouseMove = (event: MouseEvent) => {
      mousePosition.current = {
        x: (event.clientX / window.innerWidth) * 2 - 1,
        y: -(event.clientY / window.innerHeight) * 2 + 1
      };
    };
    
    // Handle window resize
    const handleResize = () => {
      camera.aspect = window.innerWidth / window.innerHeight;
      camera.updateProjectionMatrix();
      renderer.setSize(window.innerWidth, window.innerHeight);
    };
    
    // Add event listeners
    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('resize', handleResize);
    
    // Clock for consistent animation speed
    const clock = new THREE.Clock();
    
    // Animation loop
    const animate = () => {
      const animationId = requestAnimationFrame(animate);
      const delta = clock.getDelta();
      
      // Update particles
      for (let i = 0; i < particles.length; i++) {
        particles[i].update(mousePosition.current, delta);
        
        // Update particle positions in geometry
        const positionAttribute = particleGeometry.getAttribute('position') as THREE.BufferAttribute;
        positionAttribute.setXYZ(i, particles[i].position.x, particles[i].position.y, particles[i].position.z);
      }
      
      // Mark position attribute as needing update
      const positionAttribute = particleGeometry.getAttribute('position') as THREE.BufferAttribute;
      positionAttribute.needsUpdate = true;
      
      // Remove old lines
      while (linesGroup.children.length > 0) {
        const line = linesGroup.children[0];
        linesGroup.remove(line);
        (line as THREE.Line).geometry.dispose();
      }
      
      // Create new lines between nearby particles
      const connectionDistance = 5; // Maximum distance for connection
      const maxConnections = 3; // Maximum connections per particle
      
      for (let i = 0; i < particles.length; i++) {
        const particle = particles[i];
        let connections = 0;
        
        for (let j = i + 1; j < particles.length; j++) {
          if (connections >= maxConnections) break;
          
          const otherParticle = particles[j];
          const distance = particle.position.distanceTo(otherParticle.position);
          
          if (distance < connectionDistance) {
            // Create line between particles
            const lineGeometry = new THREE.BufferGeometry().setFromPoints([
              particle.position,
              otherParticle.position
            ]);
            
            // Calculate opacity based on distance
            const opacity = 0.2 * (1 - distance / connectionDistance);
            const lineMaterial = new THREE.LineBasicMaterial({
              color: 0x4a5568,
              transparent: true,
              opacity: opacity,
              blending: THREE.AdditiveBlending
            });
            
            const line = new THREE.Line(lineGeometry, lineMaterial);
            linesGroup.add(line);
            
            connections++;
          }
        }
      }
      
      // Render scene
      renderer.render(scene, camera);
    };
    
    // Start animation
    animate();
    
    // Cleanup on unmount
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('resize', handleResize);
      
      // Dispose of geometries and materials
      particleGeometry.dispose();
      particleMaterial.dispose();
      linesMaterial.dispose();
      
      // Remove renderer
      if (containerRef.current) {
        containerRef.current.removeChild(renderer.domElement);
      }
      renderer.dispose();
    };
  }, []);
  
  return <div ref={containerRef} className="fixed inset-0 z-0" />;
};

export default ParticleBackground;