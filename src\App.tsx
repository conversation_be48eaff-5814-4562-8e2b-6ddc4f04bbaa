import { useEffect, useState, useCallback } from 'react';
import { Howl } from 'howler';
import LoadingScreen from '@components/LoadingScreen';
import Cursor from '@components/Cursor';
import Navbar from '@components/Navbar';
import ParticleBackground from '@components/ParticleBackground';
import GlobalModel from '@components/GlobalModel';

// Import sections
import Hero from '@components/sections/Hero';
import About from '@components/sections/About';
import Skills from '@components/sections/Skills';
import Experience from '@components/sections/Experience';
import Projects from '@components/sections/Projects';
import Contact from '@components/sections/Contact';

// Sound effects
const sounds = {
  hover: new Howl({ src: ['/src/assets/sounds/hover.mp3'], volume: 0.2 }),
  click: new Howl({ src: ['/src/assets/sounds/click.mp3'], volume: 0.3 }),
  transition: new Howl({ src: ['/src/assets/sounds/transition.mp3'], volume: 0.4 }),
  ambient: new Howl({ 
    src: ['/src/assets/sounds/ambient.mp3'], 
    volume: 0.1, 
    loop: true,
    autoplay: false
  })
};

export type AppContext = {
  soundEnabled: boolean;
  toggleSound: () => void;
  reducedMotion: boolean;
  toggleReducedMotion: () => void;
  playSoundEffect: (type: 'hover' | 'click' | 'transition') => void;
  activeSection: string;
  scrollToSection: (sectionId: string) => void;
};

function App() {
  const [loading, setLoading] = useState(true);
  const [soundEnabled, setSoundEnabled] = useState(false);
  const [reducedMotion, setReducedMotion] = useState(false);
  const [activeSection, setActiveSection] = useState('hero');

  const toggleSound = useCallback(() => {
    setSoundEnabled(prev => !prev);
  }, []);

  const toggleReducedMotion = useCallback(() => {
    setReducedMotion(prev => !prev);
  }, []);

  // Enhanced scroll to section function
  const scrollToSection = useCallback((sectionId: string) => {
    console.log(`Scrolling to section: ${sectionId}`);
    const section = document.getElementById(sectionId);
    if (section) {
      const navbarHeight = 80; // Adjust based on your navbar height
      const elementPosition = section.getBoundingClientRect().top;
      const offsetPosition = elementPosition + window.scrollY - navbarHeight;

      console.log(`Section found, scrolling to position: ${offsetPosition}`);
      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });
      if (soundEnabled) {
        sounds.click.play();
      }
      // Do NOT setActiveSection here; let IntersectionObserver handle it
    } else {
      console.error(`Section with id "${sectionId}" not found`);
    }
  }, [soundEnabled]);

  // Improved intersection observer setup
  useEffect(() => {
    const options = {
      root: null,
      rootMargin: '-80px 0px -50% 0px', // Offset by navbar height and trigger when section is 50% visible
      threshold: [0.1, 0.5], // Multiple thresholds for better detection
    };

    const observer = new IntersectionObserver((entries) => {
      // Find the entry with the highest intersection ratio
      let mostVisibleEntry = entries.reduce((prev, current) => {
        return current.intersectionRatio > prev.intersectionRatio ? current : prev;
      });

      if (mostVisibleEntry.isIntersecting && mostVisibleEntry.intersectionRatio > 0.1) {
        const sectionId = mostVisibleEntry.target.id;
        if (activeSection !== sectionId) {
          console.log(`Active section changed from ${activeSection} to ${sectionId}`);
          setActiveSection(sectionId);
          if (soundEnabled) {
            sounds.transition.play();
          }
        }
      }
    }, options);

    // Wait for DOM to be ready, then observe sections
    const observeSections = () => {
      const sections = document.querySelectorAll('section[id]');
      if (sections.length > 0) {
        sections.forEach((section) => {
          observer.observe(section);
        });
      } else {
        // If sections not found, try again after a short delay
        setTimeout(observeSections, 100);
      }
    };

    observeSections();

    return () => {
      observer.disconnect();
    };
  }, [activeSection, soundEnabled]);

  // Sound effect handler
  const playSoundEffect = useCallback((type: 'hover' | 'click' | 'transition') => {
    if (soundEnabled && sounds[type]) {
      sounds[type].play();
    }
  }, [soundEnabled]);

  // Handle initial loading and ambient sound
  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
      if (soundEnabled) {
        sounds.ambient.play();
      }
    }, 2000);

    return () => {
      clearTimeout(timer);
      sounds.ambient.stop();
    };
  }, [soundEnabled]);

  if (loading) {
    return <LoadingScreen />;
  }

  const appContext: AppContext = {
    soundEnabled,
    toggleSound,
    reducedMotion,
    toggleReducedMotion,
    playSoundEffect,
    activeSection,
    scrollToSection
  };

  return (
    <div className="bg-[#050816] text-white overflow-x-hidden relative">
      <ParticleBackground enabled={!reducedMotion} />
      <Cursor enabled={!reducedMotion} />

      {/* Global 3D Model */}
      <GlobalModel activeSection={activeSection} />

      <Navbar
        playSoundEffect={playSoundEffect}
        activeSection={activeSection}
        scrollToSection={scrollToSection}
      />
      <main className="relative z-20">
        <Hero appContext={appContext} />
        <About appContext={appContext} />
        <Skills appContext={appContext} />
        <Experience appContext={appContext} />
        <Projects appContext={appContext} />
        <Contact appContext={appContext} />
      </main>
    </div>
  );
}

export default App;