import { useEffect, useState, useCallback } from 'react';
import { Howl } from 'howler';
import LoadingScreen from '@components/LoadingScreen';
import Cursor from '@components/Cursor';
import Navbar from '@components/Navbar';
import ParticleBackground from '@components/ParticleBackground';
import GlobalModel from '@components/GlobalModel';

// Import sections
import Hero from '@components/sections/Hero';
import About from '@components/sections/About';
import Skills from '@components/sections/Skills';
import Experience from '@components/sections/Experience';
import Projects from '@components/sections/Projects';
import Contact from '@components/sections/Contact';

// Sound effects
const sounds = {
  hover: new Howl({ src: ['/src/assets/sounds/hover.mp3'], volume: 0.2 }),
  click: new Howl({ src: ['/src/assets/sounds/click.mp3'], volume: 0.3 }),
  transition: new Howl({ src: ['/src/assets/sounds/transition.mp3'], volume: 0.4 }),
  ambient: new Howl({ 
    src: ['/src/assets/sounds/ambient.mp3'], 
    volume: 0.1, 
    loop: true,
    autoplay: false
  })
};

export type AppContext = {
  soundEnabled: boolean;
  toggleSound: () => void;
  reducedMotion: boolean;
  toggleReducedMotion: () => void;
  playSoundEffect: (type: 'hover' | 'click' | 'transition') => void;
  activeSection: string;
  scrollToSection: (sectionId: string) => void;
};

function App() {
  const [loading, setLoading] = useState(true);
  const [soundEnabled, setSoundEnabled] = useState(false);
  const [reducedMotion, setReducedMotion] = useState(false);
  const [activeSection, setActiveSection] = useState('hero');

  const toggleSound = useCallback(() => {
    setSoundEnabled(prev => !prev);
  }, []);

  const toggleReducedMotion = useCallback(() => {
    setReducedMotion(prev => !prev);
  }, []);

  // Enhanced scroll to section function
  const scrollToSection = useCallback((sectionId: string) => {
    console.log(`Attempting to scroll to section: ${sectionId}`);

    // Wait a bit for DOM to be ready
    setTimeout(() => {
      const section = document.getElementById(sectionId);
      console.log(`Section element found:`, section);

      if (section) {
        const navbarHeight = 80;
        const rect = section.getBoundingClientRect();
        const offsetPosition = window.scrollY + rect.top - navbarHeight;

        console.log(`Scrolling to position: ${offsetPosition} (current: ${window.scrollY})`);

        // Use both methods for better compatibility
        section.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
          inline: 'nearest'
        });

        // Fallback method
        window.scrollTo({
          top: Math.max(0, offsetPosition),
          behavior: 'smooth'
        });

        if (soundEnabled) {
          sounds.click.play();
        }

        // Update active section
        setActiveSection(sectionId);
      } else {
        console.error(`Section with id "${sectionId}" not found`);
        console.log('Available sections:', Array.from(document.querySelectorAll('section[id]')).map(s => s.id));
      }
    }, 100);
  }, [soundEnabled]);

  // Improved scroll detection with manual calculation
  useEffect(() => {
    const handleScroll = () => {
      const sections = document.querySelectorAll('section[id]');
      const scrollPosition = window.scrollY + window.innerHeight / 3; // Use 1/3 of viewport height as trigger

      let newActiveSection = 'hero';

      // Simple approach: find which section the scroll position is currently in
      sections.forEach((section) => {
        const sectionTop = (section as HTMLElement).offsetTop;
        const sectionHeight = section.clientHeight;
        const sectionId = section.getAttribute('id') || '';
        const sectionBottom = sectionTop + sectionHeight;

        // Check if scroll position is within this section
        if (scrollPosition >= sectionTop && scrollPosition < sectionBottom) {
          newActiveSection = sectionId;
        }
      });

      // Fallback: if no section found, find the closest one
      if (newActiveSection === 'hero' && window.scrollY > 100) {
        let minDistance = Infinity;
        sections.forEach((section) => {
          const sectionTop = (section as HTMLElement).offsetTop;
          const sectionId = section.getAttribute('id') || '';
          const distance = Math.abs(scrollPosition - sectionTop);

          if (distance < minDistance) {
            minDistance = distance;
            newActiveSection = sectionId;
          }
        });
      }

      if (activeSection !== newActiveSection) {
        console.log(`Active section changed from ${activeSection} to ${newActiveSection} (scroll: ${scrollPosition})`);
        console.log('Available sections:', Array.from(sections).map(s => ({ id: s.id, top: (s as HTMLElement).offsetTop, height: s.clientHeight })));
        setActiveSection(newActiveSection);
        if (soundEnabled) {
          sounds.transition.play();
        }
      }
    };

    // Throttle scroll events
    let ticking = false;
    const throttledScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', throttledScroll, { passive: true });
    handleScroll(); // Initial call

    return () => window.removeEventListener('scroll', throttledScroll);
  }, [activeSection, soundEnabled]);

  // Sound effect handler
  const playSoundEffect = useCallback((type: 'hover' | 'click' | 'transition') => {
    if (soundEnabled && sounds[type]) {
      sounds[type].play();
    }
  }, [soundEnabled]);

  // Handle initial loading and ambient sound
  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
      if (soundEnabled) {
        sounds.ambient.play();
      }
    }, 2000);

    return () => {
      clearTimeout(timer);
      sounds.ambient.stop();
    };
  }, [soundEnabled]);

  if (loading) {
    return <LoadingScreen />;
  }

  const appContext: AppContext = {
    soundEnabled,
    toggleSound,
    reducedMotion,
    toggleReducedMotion,
    playSoundEffect,
    activeSection,
    scrollToSection
  };

  return (
    <div className="bg-[#050816] text-white overflow-x-hidden relative">
      <ParticleBackground enabled={!reducedMotion} />
      <Cursor enabled={!reducedMotion} />

      {/* Debug: Show current active section */}
      <div className="fixed top-20 left-4 z-50 bg-black/80 text-white p-2 rounded text-sm">
        Active: {activeSection}
      </div>

      {/* Global 3D Model */}
      <GlobalModel activeSection={activeSection} />

      <Navbar
        playSoundEffect={playSoundEffect}
        activeSection={activeSection}
        scrollToSection={scrollToSection}
      />
      <main className="relative z-20">
        <section id="hero" className="min-h-screen">
          <Hero appContext={appContext} />
        </section>
        <section id="about" className="min-h-screen">
          <About appContext={appContext} />
        </section>
        <section id="skills" className="min-h-screen">
          <Skills appContext={appContext} />
        </section>
        <section id="experience" className="min-h-screen">
          <Experience appContext={appContext} />
        </section>
        <section id="projects" className="min-h-screen">
          <Projects appContext={appContext} />
        </section>
        <section id="contact" className="min-h-screen">
          <Contact appContext={appContext} />
        </section>
      </main>
    </div>
  );
}

export default App;