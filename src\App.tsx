import { useEffect, useState, useRef } from 'react';
import { Howl } from 'howler';
import LoadingScreen from '@components/LoadingScreen';
import Cursor from '@components/Cursor';
import Navbar from '@components/Navbar';
import ParticleBackground from '@components/ParticleBackground';

// Import sections directly
import Hero from '@components/sections/Hero';
import About from '@components/sections/About';
import Skills from '@components/sections/Skills';
import Experience from '@components/sections/Experience';
import Projects from '@components/sections/Projects';
import Contact from '@components/sections/Contact';

// Sound effects
const sounds = {
  hover: new Howl({ src: ['/src/assets/sounds/hover.mp3'], volume: 0.2 }),
  click: new Howl({ src: ['/src/assets/sounds/click.mp3'], volume: 0.3 }),
  transition: new Howl({ src: ['/src/assets/sounds/transition.mp3'], volume: 0.4 }),
  ambient: new Howl({ 
    src: ['/src/assets/sounds/ambient.mp3'], 
    volume: 0.1, 
    loop: true,
    autoplay: false
  })
};

function App() {
  const [loading, setLoading] = useState(true);
  const [soundEnabled, setSoundEnabled] = useState(false);
  const [reducedMotion, setReducedMotion] = useState(false);
  const [activeSection, setActiveSection] = useState('hero');
  const sectionsRef = useRef<HTMLDivElement>(null);
  
  // Handle initial loading
  useEffect(() => {
    // Simulate loading assets
    const timer = setTimeout(() => {
      setLoading(false);
      // Start ambient sound when loading completes (if enabled)
      if (soundEnabled) {
        sounds.ambient.play();
      }
    }, 3000); // 3 second loading screen
    
    return () => clearTimeout(timer);
  }, [soundEnabled]);
  
  // Handle sound toggle
  const toggleSound = () => {
    setSoundEnabled(prev => {
      const newState = !prev;
      if (newState) {
        sounds.ambient.play();
      } else {
        sounds.ambient.pause();
      }
      return newState;
    });
  };
  
  // Handle reduced motion toggle
  const toggleReducedMotion = () => {
    setReducedMotion(prev => !prev);
  };
  
  // Play sound effects
  const playSoundEffect = (type: 'hover' | 'click' | 'transition') => {
    if (soundEnabled && sounds[type]) {
      sounds[type].play();
    }
  };
  
  // Provide sound and animation context to all components
  const appContext = {
    soundEnabled,
    toggleSound,
    reducedMotion,
    toggleReducedMotion,
    playSoundEffect
  };
  
  // Handle scroll to detect active section
  useEffect(() => {
    const handleScroll = () => {
      if (!sectionsRef.current) return;

      const sections = sectionsRef.current.querySelectorAll('section[id]');
      const navbarHeight = 80;
      const scrollPosition = window.scrollY + navbarHeight + 100; // Add some offset for better detection

      let currentSection = 'hero'; // Default to hero section

      sections.forEach((section) => {
        const sectionTop = (section as HTMLElement).offsetTop;
        const sectionHeight = section.clientHeight;
        const sectionId = section.getAttribute('id') || '';

        // Check if the scroll position is within this section
        if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
          currentSection = sectionId;
        }
      });

      setActiveSection(currentSection);
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // Check initial position

    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Smooth scroll to section
  const scrollToSection = (sectionId: string) => {
    playSoundEffect('transition');
    const section = document.getElementById(sectionId);
    if (section) {
      // Account for fixed navbar height (approximately 80px)
      const navbarHeight = 80;
      const targetPosition = section.offsetTop - navbarHeight;

      window.scrollTo({
        top: Math.max(0, targetPosition), // Ensure we don't scroll to negative position
        behavior: 'smooth'
      });
    }
  };
  
  if (loading) {
    return <LoadingScreen />;
  }

  return (
    <div className="app-container bg-[#0a0f1d] text-white min-h-screen relative">
      {/* Custom cursor */}
      <Cursor enabled={!reducedMotion} />
      
      {/* Particle background */}
      <ParticleBackground enabled={!reducedMotion} />
      
      
      {/* Navigation */}
      <Navbar 
        playSoundEffect={playSoundEffect} 
        activeSection={activeSection}
        scrollToSection={scrollToSection}
      />
      
      {/* Floating navigation dots - REMOVED as per tasks.md */}
      
      {/* Main content */}
      <main className="relative z-10" ref={sectionsRef}>
        <Hero appContext={appContext} />
        <About appContext={appContext} />
        <Skills appContext={appContext} />
        <Experience appContext={appContext} />
        <Projects appContext={appContext} />
        <Contact appContext={appContext} />
      </main>
    </div>
  );
}

export default App;