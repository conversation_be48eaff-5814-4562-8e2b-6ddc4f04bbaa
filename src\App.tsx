import { useEffect, useState, useRef, useCallback } from 'react';
import { Howl } from 'howler';
import LoadingScreen from '@components/LoadingScreen';
import Cursor from '@components/Cursor';
import Navbar from '@components/Navbar';
import ParticleBackground from '@components/ParticleBackground';

// Import sections
import Hero from '@components/sections/Hero';
import About from '@components/sections/About';
import Skills from '@components/sections/Skills';
import Experience from '@components/sections/Experience';
import Projects from '@components/sections/Projects';
import Contact from '@components/sections/Contact';

// Sound effects
const sounds = {
  hover: new Howl({ src: ['/src/assets/sounds/hover.mp3'], volume: 0.2 }),
  click: new Howl({ src: ['/src/assets/sounds/click.mp3'], volume: 0.3 }),
  transition: new Howl({ src: ['/src/assets/sounds/transition.mp3'], volume: 0.4 }),
  ambient: new Howl({ 
    src: ['/src/assets/sounds/ambient.mp3'], 
    volume: 0.1, 
    loop: true,
    autoplay: false
  })
};

export type AppContext = {
  soundEnabled: boolean;
  toggleSound: () => void;
  reducedMotion: boolean;
  toggleReducedMotion: () => void;
  playSoundEffect: (type: 'hover' | 'click' | 'transition') => void;
  activeSection: string;
  scrollToSection: (sectionId: string) => void;
};

function App() {
  const [loading, setLoading] = useState(true);
  const [soundEnabled, setSoundEnabled] = useState(false);
  const [reducedMotion, setReducedMotion] = useState(false);
  const [activeSection, setActiveSection] = useState('hero');

  const toggleSound = useCallback(() => {
    setSoundEnabled(prev => !prev);
  }, []);

  const toggleReducedMotion = useCallback(() => {
    setReducedMotion(prev => !prev);
  }, []);

  // Enhanced scroll to section function
  const scrollToSection = useCallback((sectionId: string) => {
    const section = document.getElementById(sectionId);
    if (section) {
      const navbarHeight = 80; // Adjust based on your navbar height
      const elementPosition = section.getBoundingClientRect().top;
      const offsetPosition = elementPosition + window.scrollY - navbarHeight;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });
      if (soundEnabled) {
        sounds.click.play();
      }
      // Do NOT setActiveSection here; let IntersectionObserver handle it
    }
  }, [soundEnabled]);

  // Improved intersection observer setup
  useEffect(() => {
    const options = {
      root: null,
      rootMargin: '-80px 0px 0px 0px', // Offset by navbar height
      threshold: 0.2, // Trigger earlier for better UX
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const sectionId = entry.target.id;
          if (activeSection !== sectionId) {
            setActiveSection(sectionId);
            if (soundEnabled) {
              sounds.transition.play();
            }
          }
        }
      });
    }, options);

    // Observe all sections
    const sections = document.querySelectorAll('section[id]');
    sections.forEach((section) => {
      observer.observe(section);
    });

    return () => {
      sections.forEach((section) => observer.unobserve(section));
      observer.disconnect();
    };
  }, [activeSection, soundEnabled]);

  // Sound effect handler
  const playSoundEffect = useCallback((type: 'hover' | 'click' | 'transition') => {
    if (soundEnabled && sounds[type]) {
      sounds[type].play();
    }
  }, [soundEnabled]);

  // Handle initial loading and ambient sound
  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
      if (soundEnabled) {
        sounds.ambient.play();
      }
    }, 2000);

    return () => {
      clearTimeout(timer);
      sounds.ambient.stop();
    };
  }, [soundEnabled]);

  if (loading) {
    return <LoadingScreen />;
  }

  const appContext: AppContext = {
    soundEnabled,
    toggleSound,
    reducedMotion,
    toggleReducedMotion,
    playSoundEffect,
    activeSection,
    scrollToSection
  };

  return (
    <div className="bg-[#050816] text-white overflow-x-hidden">
      <ParticleBackground enabled={!reducedMotion} />
      <Cursor enabled={!reducedMotion} />
      <Navbar
        playSoundEffect={playSoundEffect}
        activeSection={activeSection}
        scrollToSection={scrollToSection}
      />
      <main>
        <section id="hero" className="min-h-screen"><Hero appContext={appContext} /></section>
        <section id="about" className="min-h-screen"><About appContext={appContext} /></section>
        <section id="skills" className="min-h-screen"><Skills appContext={appContext} /></section>
        <section id="experience" className="min-h-screen"><Experience appContext={appContext} /></section>
        <section id="projects" className="min-h-screen"><Projects appContext={appContext} /></section>
        <section id="contact" className="min-h-screen"><Contact appContext={appContext} /></section>
      </main>
    </div>
  );
}

export default App;