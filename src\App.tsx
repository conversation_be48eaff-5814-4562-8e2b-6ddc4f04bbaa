import { useEffect, useState, useRef } from 'react';
import { Howl } from 'howler';
import LoadingScreen from '@components/LoadingScreen';
import Cursor from '@components/Cursor';
import Navbar from '@components/Navbar';
import ParticleBackground from '@components/ParticleBackground';

// Import sections directly
import Hero from '@components/sections/Hero';
import About from '@components/sections/About';
import Skills from '@components/sections/Skills';
import Experience from '@components/sections/Experience';
import Projects from '@components/sections/Projects';
import Contact from '@components/sections/Contact';

// Sound effects
const sounds = {
  hover: new Howl({ src: ['/src/assets/sounds/hover.mp3'], volume: 0.2 }),
  click: new Howl({ src: ['/src/assets/sounds/click.mp3'], volume: 0.3 }),
  transition: new Howl({ src: ['/src/assets/sounds/transition.mp3'], volume: 0.4 }),
  ambient: new Howl({ 
    src: ['/src/assets/sounds/ambient.mp3'], 
    volume: 0.1, 
    loop: true,
    autoplay: false
  })
};

function App() {
  const [loading, setLoading] = useState(true);
  const [soundEnabled, setSoundEnabled] = useState(false);
  const [reducedMotion, setReducedMotion] = useState(false);
  const [activeSection, setActiveSection] = useState('hero');
  const [isScrolling, setIsScrolling] = useState(false);

  // Debug activeSection changes
  useEffect(() => {
    console.log('Active section changed to:', activeSection);
  }, [activeSection]);
  const sectionsRef = useRef<HTMLDivElement>(null);
  
  // Handle initial loading
  useEffect(() => {
    // Simulate loading assets
    const timer = setTimeout(() => {
      setLoading(false);
      // Start ambient sound when loading completes (if enabled)
      if (soundEnabled) {
        sounds.ambient.play();
      }
    }, 3000); // 3 second loading screen
    
    return () => clearTimeout(timer);
  }, [soundEnabled]);
  
  // Handle sound toggle
  const toggleSound = () => {
    setSoundEnabled(prev => {
      const newState = !prev;
      if (newState) {
        sounds.ambient.play();
      } else {
        sounds.ambient.pause();
      }
      return newState;
    });
  };
  
  // Handle reduced motion toggle
  const toggleReducedMotion = () => {
    setReducedMotion(prev => !prev);
  };
  
  // Play sound effects
  const playSoundEffect = (type: 'hover' | 'click' | 'transition') => {
    if (soundEnabled && sounds[type]) {
      sounds[type].play();
    }
  };
  
  // Provide sound and animation context to all components
  const appContext = {
    soundEnabled,
    toggleSound,
    reducedMotion,
    toggleReducedMotion,
    playSoundEffect
  };
  
  // Handle scroll to detect active section - simplified approach
  useEffect(() => {
    if (!sectionsRef.current) return;

    const sections = sectionsRef.current.querySelectorAll('section[id]');
    console.log('Found sections:', sections.length);
    sections.forEach(section => {
      console.log('Section found:', section.getAttribute('id'));
    });

    // Simple scroll detection with throttling
    let ticking = false;

    const handleScroll = () => {
      if (!ticking && !isScrolling) {
        requestAnimationFrame(() => {
          const scrollPosition = window.scrollY + 120; // Offset for navbar
          let currentSection = 'hero';

          sections.forEach((section) => {
            const sectionElement = section as HTMLElement;
            const sectionTop = sectionElement.offsetTop;
            const sectionId = sectionElement.getAttribute('id') || '';

            if (scrollPosition >= sectionTop) {
              currentSection = sectionId;
            }
          });

          console.log('Scroll detection - current section:', currentSection);
          setActiveSection(currentSection);
          ticking = false;
        });
        ticking = true;
      }
    };

    // Add scroll listener
    window.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll(); // Initial check

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Smooth scroll to section
  const scrollToSection = (sectionId: string) => {
    console.log('Scrolling to section:', sectionId);

    const section = document.getElementById(sectionId);
    console.log('Found section element:', section);

    if (section) {
      const navbarHeight = 80;
      const targetPosition = section.offsetTop - navbarHeight;

      console.log('Current scroll position:', window.scrollY);
      console.log('Target position:', targetPosition);

      // Set scrolling flag to prevent scroll detection conflicts
      setIsScrolling(true);

      // Immediately update active section for better UX
      setActiveSection(sectionId);

      // Use simple scrollTo for reliability
      window.scrollTo({
        top: targetPosition,
        behavior: 'smooth'
      });

      // Clear scrolling flag after scroll completes
      setTimeout(() => {
        setIsScrolling(false);
      }, 1000);

      console.log('Scroll executed to:', sectionId);
    } else {
      console.warn(`Section with id "${sectionId}" not found`);
    }
  };

  // Test function for debugging
  const testScroll = () => {
    console.log('Test scroll function called');
    window.scrollTo({ top: 1000, behavior: 'smooth' });
  };

  // Make functions available globally for debugging
  (window as any).testScroll = testScroll;
  (window as any).scrollToSection = scrollToSection;

  if (loading) {
    return <LoadingScreen />;
  }

  return (
    <div className="app-container bg-[#0a0f1d] text-white min-h-screen relative">
      {/* Custom cursor */}
      <Cursor enabled={!reducedMotion} />
      
      {/* Particle background */}
      <ParticleBackground enabled={!reducedMotion} />
      
      
      {/* Navigation */}
      <Navbar 
        playSoundEffect={playSoundEffect} 
        activeSection={activeSection}
        scrollToSection={scrollToSection}
      />
      
      {/* Floating navigation dots - REMOVED as per tasks.md */}
      
      {/* Main content */}
      <main className="relative z-10" ref={sectionsRef}>
        <Hero appContext={appContext} />
        <About appContext={appContext} />
        <Skills appContext={appContext} />
        <Experience appContext={appContext} />
        <Projects appContext={appContext} />
        <Contact appContext={appContext} />
      </main>
    </div>
  );
}

export default App;