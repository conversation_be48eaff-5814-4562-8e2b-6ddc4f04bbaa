# 🧠 Claude Sonnet 3.5 Prompt: Portfolio Issues with Scroll, Sections, and 3D Model

I’m working on a portfolio site built with **React + Vite + @react-three/fiber + @react-three/drei**.  
I have multiple issues related to navigation, scroll detection, and 3D rendering that I need your help to resolve.

---

## ❌ Problems I’m Facing:

### 1. Navbar section scroll is not working

- When I click a navbar link (e.g., "About", "Projects", etc.), the page does not scroll to that section.
- I’m using `<section id="...">` and `onClick` handlers on navbar items with `document.getElementById(...).scrollIntoView()` or anchor links — but nothing happens or it scrolls incorrectly.
- I want smooth scrolling to each section on navbar click.

---

### 2. `activeSection` state is not updating on scroll

- I have logic to detect which section is currently in view (e.g., using `IntersectionObserver` or `scrollY`), and I store that in `activeSection`.
- But it’s not updating correctly as I scroll through the sections.
- Also, I want to **remove the section navigation dots on the right side**, which are not working and are visually broken.

---

### 3. 3D `.glb` model is not rendering

- Initially, the model rendered once, but now it doesn’t show up at all.
- I’m loading it using `useGLTF('/models/sci-fi-robot.glb')` and rendering with `<primitive object={scene} />` inside a `<Canvas>`.
- The model has no baked animation. I want to fix the visibility and apply scroll-based animations (e.g., moving arms or rotating head on scroll).
- I also want proper lighting and camera positioning so the model is always visible.
- I have logged the model’s scene structure using `console.log(scene)` in the browser and can see the objects and hierarchy.

---

## ✅ Goals

- Fix **navbar click-to-scroll** so it navigates smoothly to the target section.
- Fix **scroll detection logic** so `activeSection` updates as user scrolls.
- Remove the **broken section dots navigation** on the right side.
- Ensure the **3D model** is loaded, visible, and animates via code (not external tools).
- Use `useRef()` and `useFrame()` for scroll-based transformations.

---

## 💡 Technologies in use

- React + Vite
- @react-three/fiber
- @react-three/drei
- `useGLTF()` for model loading
- Possibly `react-scroll`, `IntersectionObserver`, or custom scroll detection

---

## 📌 Request

Please:

- Fix or rewrite the relevant code logic
- Explain any common mistakes (like scale, camera position, light)
- Give clean examples for scroll-based model animation and section syncing
- Make sure while updating these changes, **other working code won’t break**
