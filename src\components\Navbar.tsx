import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface NavbarProps {
  playSoundEffect: (type: 'hover' | 'click' | 'transition') => void;
  activeSection: string;
  scrollToSection: (sectionId: string) => void;
}

const Navbar: React.FC<NavbarProps> = ({ playSoundEffect, activeSection, scrollToSection }) => {
  const [isOpen, setIsOpen] = useState(false);

  // Debug activeSection in navbar
  useEffect(() => {
    console.log('Navbar received activeSection:', activeSection);
  }, [activeSection]);

  // Close mobile menu when section changes
  useEffect(() => {
    setIsOpen(false);
  }, [activeSection]);
  
  // Handle link hover
  const handleLinkHover = () => {
    playSoundEffect('hover');
  };
  
  // Handle link click
  const handleLinkClick = () => {
    playSoundEffect('click');
  };
  
  // Navigation links
  const navLinks = [
    { id: 'hero', label: 'Home' },
    { id: 'about', label: 'About' },
    { id: 'skills', label: 'Skills' },
    { id: 'experience', label: 'Experience' },
    { id: 'projects', label: 'Projects' },
    { id: 'contact', label: 'Contact' },
  ];
  
  return (
    <header className="fixed top-0 left-0 w-full z-40 bg-[#0a0f1d]/80 backdrop-blur-md">
      <div className="container mx-auto px-4 py-4 flex justify-between items-center">
        {/* Logo */}
        <button 
          className="text-white font-bold text-xl font-space-grotesk text-left"
          onMouseEnter={handleLinkHover}
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log('Logo clicked - scrolling to hero');
            handleLinkClick();
            scrollToSection('hero');
          }}
        >
          <span className="text-[#00ff9e]">D</span>ishang <span className="text-[#00ff9e]">P</span>atel
        </button>
        
        {/* Desktop Navigation */}
        <nav className="hidden md:flex space-x-8">
          {navLinks.map((link) => (
            <button
              key={link.id}
              className={`relative text-sm font-medium transition-colors ${activeSection === link.id ? 'text-[#00ff9e]' : 'text-gray-300 hover:text-white'}`}
              onMouseEnter={handleLinkHover}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('Navbar link clicked:', link.id);
                handleLinkClick();
                scrollToSection(link.id);
              }}
            >
              {link.label}
              {activeSection === link.id && (
                <motion.span
                  className="absolute -bottom-1 left-0 w-full h-0.5 bg-[#00ff9e]"
                  layoutId="navbar-indicator"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                />
              )}
            </button>
          ))}
        </nav>
        
        {/* Mobile Menu Button */}
        <button
          className="md:hidden text-white focus:outline-none"
          onClick={() => {
            setIsOpen(!isOpen);
            playSoundEffect('click');
          }}
          aria-label={isOpen ? 'Close menu' : 'Open menu'}
          data-magnetic
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            {isOpen ? (
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            ) : (
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 6h16M4 12h16M4 18h16"
              />
            )}
          </svg>
        </button>
      </div>
      
      {/* Mobile Navigation */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="md:hidden bg-[#0a0f1d] absolute w-full"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
          >
            <div className="container mx-auto px-4 py-4">
              <nav className="flex flex-col space-y-4">
                {navLinks.map((link) => (
                  <button
                    key={link.id}
                    className={`text-sm font-medium transition-colors text-left ${activeSection === link.id ? 'text-[#00ff9e]' : 'text-gray-300 hover:text-white'}`}
                    onMouseEnter={handleLinkHover}
                    onClick={(e) => {
                      e.preventDefault();
                      console.log('Mobile nav link clicked:', link.id);
                      handleLinkClick();
                      scrollToSection(link.id);
                      setIsOpen(false);
                    }}
                  >
                    {link.label}
                  </button>
                ))}
              </nav>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </header>
  );
};

export default Navbar;