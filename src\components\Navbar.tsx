import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface NavbarProps {
  playSoundEffect: (type: 'hover' | 'click' | 'transition') => void;
  activeSection: string;
  scrollToSection: (sectionId: string) => void;
}

const Navbar: React.FC<NavbarProps> = ({ playSoundEffect, activeSection, scrollToSection }) => {
  const [isOpen, setIsOpen] = useState(false);
  
  // Close mobile menu when section changes
  useEffect(() => {
    setIsOpen(false);
  }, [activeSection]);

  const navLinks = [
    { id: 'hero', label: 'Home' },
    { id: 'about', label: 'About' },
    { id: 'skills', label: 'Skills' },
    { id: 'experience', label: 'Experience' },
    { id: 'projects', label: 'Projects' },
    { id: 'contact', label: 'Contact' },
  ];
  
  return (
    <header className="fixed top-0 left-0 w-full z-40 bg-[#0a0f1d]/80 backdrop-blur-md">
      <nav className="container mx-auto px-4 py-4">
        <div className="flex justify-between items-center">
          {/* Logo */}
          <motion.a
            href="#hero"
            className="text-2xl font-bold text-green-400"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={(e) => {
              e.preventDefault();
              scrollToSection('hero');
            }}
          >
            Dishang
          </motion.a>

          {/* Desktop Navigation */}
          <div className="hidden md:flex space-x-8">
            {navLinks.map(({ id, label }) => (
              <motion.a
                key={id}
                href={`#${id}`}
                className={`text-lg transition-colors ${
                  activeSection === id
                    ? 'text-green-400 font-semibold'
                    : 'text-gray-300 hover:text-white'
                }`}
                whileHover={{ y: -2 }}
                whileTap={{ y: 0 }}
                onClick={(e) => {
                  e.preventDefault();
                  scrollToSection(id);
                }}
                onMouseEnter={() => playSoundEffect('hover')}
              >
                {label}
              </motion.a>
            ))}
          </div>

          {/* Mobile Menu Button */}
          <motion.button
            className="md:hidden p-2"
            whileTap={{ scale: 0.9 }}
            onClick={() => {
              setIsOpen(!isOpen);
              playSoundEffect('click');
            }}
          >
            <div className="w-6 h-5 flex flex-col justify-between">
              <span className={`w-full h-0.5 bg-white transition-transform ${isOpen ? 'rotate-45 translate-y-2' : ''}`} />
              <span className={`w-full h-0.5 bg-white transition-opacity ${isOpen ? 'opacity-0' : ''}`} />
              <span className={`w-full h-0.5 bg-white transition-transform ${isOpen ? '-rotate-45 -translate-y-2' : ''}`} />
            </div>
          </motion.button>
        </div>

        {/* Mobile Menu */}
        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              className="md:hidden mt-4"
            >
              <div className="flex flex-col space-y-4">
                {navLinks.map(({ id, label }) => (
                  <motion.a
                    key={id}
                    href={`#${id}`}
                    className={`text-lg transition-colors ${
                      activeSection === id
                        ? 'text-green-400 font-semibold'
                        : 'text-gray-300'
                    }`}
                    whileHover={{ x: 4 }}
                    onClick={(e) => {
                      e.preventDefault();
                      scrollToSection(id);
                    }}
                    onMouseEnter={() => playSoundEffect('hover')}
                  >
                    {label}
                  </motion.a>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </nav>
    </header>
  );
};

export default Navbar;