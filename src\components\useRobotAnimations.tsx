import { useMemo } from 'react';
import * as THREE from 'three';

export const useRobotAnimations = (modelRef: any) => {
  const animations = useMemo(() => {
    if (!modelRef.current) return {};

    const createClip = (name: string, tracks: THREE.KeyframeTrack[]) => {
      return new THREE.AnimationClip(name, -1, tracks);
    };

    const idleClip = createClip('idle', [
      new THREE.NumberKeyframeTrack(
        '.rotation[x]',
        [0, 1, 2],
        [0, 0.1, 0]
      ),
    ]);

    const waveClip = createClip('wave', [
      new THREE.NumberKeyframeTrack(
        '.getObjectByName("mixamorigRightArm").rotation[z]',
        [0, 0.5, 1, 1.5, 2],
        [0, -1, 0, -1, 0]
      ),
    ]);

    const pointingClip = createClip('pointing', [
      new THREE.NumberKeyframeTrack(
        '.getObjectByName("mixamorigRightArm").rotation[x]',
        [0, 0.5, 1.5, 2],
        [0, -1.5, -1.5, 0]
      ),
      new THREE.NumberKeyframeTrack(
        '.getObjectByName("mixamorigRightArm").rotation[y]',
        [0, 0.5, 1.5, 2],
        [0, -0.5, -0.5, 0]
      ),
    ]);

    const watchClip = createClip('watch', [
      new THREE.NumberKeyframeTrack(
        '.getObjectByName("mixamorigRightHand").rotation[x]',
        [0, 0.5, 1.5, 2],
        [0, -1, -1, 0]
      ),
    ]);

    const workingClip = createClip('working', [
      new THREE.NumberKeyframeTrack(
        '.getObjectByName("mixamorigSpine").rotation[y]',
        [0, 1, 2, 3, 4],
        [0, 0.5, -0.5, 0.5, 0]
      ),
      new THREE.NumberKeyframeTrack(
        '.getObjectByName("mixamorigRightArm").rotation[x]',
        [0, 1, 2, 3, 4],
        [0, -0.5, -0.5, -0.5, 0]
      ),
      new THREE.NumberKeyframeTrack(
        '.getObjectByName("mixamorigLeftArm").rotation[x]',
        [0, 1, 2, 3, 4],
        [0, -0.5, -0.5, -0.5, 0]
      ),
    ]);

    const contactClip = createClip('contact', [
      new THREE.NumberKeyframeTrack(
        '.getObjectByName("mixamorigHead").rotation[y]',
        [0, 1, 2, 3, 4],
        [0, 0.5, -0.5, 0.5, 0]
      ),
    ]);

    return {
      idle: idleClip,
      wave: waveClip,
      pointing: pointingClip,
      watch: watchClip,
      working: workingClip,
      contact: contactClip,
    };
  }, [modelRef]);

  return animations;
};
