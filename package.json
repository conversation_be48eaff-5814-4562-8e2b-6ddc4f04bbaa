{"name": "dishang-portfolio", "version": "1.0.0", "description": "<PERSON><PERSON><PERSON>'s WebGL Portfolio", "main": "index.js", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@react-three/drei": "^9.88.13", "@react-three/fiber": "^8.15.11", "@react-three/postprocessing": "^2.15.11", "framer-motion": "^10.16.5", "gsap": "^3.12.2", "howler": "^2.2.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-error-boundary": "^6.0.0", "react-router-dom": "^7.6.3", "tailwindcss": "^3.3.5", "three": "^0.158.0"}, "devDependencies": {"@types/howler": "^2.2.12", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/three": "^0.158.3", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "typescript": "^5.2.2", "vite": "^5.0.0"}}