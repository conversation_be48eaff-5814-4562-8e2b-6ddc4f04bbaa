import { useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import info from '@/assets/info.json';

interface ExperienceProps {
  appContext: {
    soundEnabled: boolean;
    toggleSound: () => void;
    reducedMotion: boolean;
    toggleReducedMotion: () => void;
    playSoundEffect: (type: 'hover' | 'click' | 'transition') => void;
  };
}

const Experience: React.FC<ExperienceProps> = ({ appContext }) => {
  const { reducedMotion, playSoundEffect } = appContext;
  const timelineRef = useRef<HTMLDivElement>(null);
  const inView = useInView(timelineRef, { once: true, amount: 0.2 });
  
  // Sort experiences by date (most recent first)
  const sortedExperiences = [...info.experiences].sort((a, b) => {
    // Parse duration strings to compare
    const getEndYear = (duration: string) => {
      const years = duration.split(' - ')[1];
      return years === 'current' ? new Date().getFullYear() : parseInt(years);
    };
    return getEndYear(b.duration) - getEndYear(a.duration);
  });
  
  // Format date for display
  const formatDate = (duration: string) => duration;
  
  return (
    <section id="experience" className="min-h-screen pt-24 pb-16 px-4 relative overflow-hidden">
      <div className="container mx-auto max-w-5xl relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="mb-12 text-center"
        >
          <h2 className="text-4xl md:text-5xl font-bold font-space-grotesk mb-4">
            Work <span className="text-[#00ff9e]">Experience</span>
          </h2>
          <div className="w-24 h-1 bg-[#00ff9e] mx-auto rounded-full" />
        </motion.div>
        
        {/* Timeline */}
        <div ref={timelineRef} className="relative">
          {/* Timeline line */}
          <div className="absolute left-0 md:left-1/2 transform md:-translate-x-1/2 h-full w-1 bg-gradient-to-b from-[#00ff9e]/80 to-[#00ff9e]/20 rounded-full" />
          
          {sortedExperiences.map((exp, index) => {
            const isEven = index % 2 === 0;
            
            return (
              <motion.div
                key={`${exp.company}-${index}`}
                className={`flex flex-col md:flex-row mb-12 relative ${isEven ? 'md:flex-row-reverse' : ''}`}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.7, delay: index * 0.1 }}
              >
                {/* Timeline dot */}
                <div className="absolute left-0 md:left-1/2 transform -translate-x-1/2 w-5 h-5 rounded-full bg-[#00ff9e] border-4 border-[#0a0f1d] z-10" />
                
                {/* Date */}
                <div className={`md:w-1/2 pb-8 md:pb-0 ${isEven ? 'md:pl-12' : 'md:pr-12 md:text-right'}`}>
                  <motion.div
                    className="inline-block px-4 py-1 rounded-full bg-[#00ff9e]/10 border border-[#00ff9e]/30 text-[#00ff9e] font-medium mb-2"
                    whileHover={{ scale: 1.05 }}
                    onHoverStart={() => playSoundEffect('hover')}
                  >
                    {exp.duration}
                  </motion.div>
                </div>
                
                {/* Content */}
                <div className={`md:w-1/2 ${isEven ? 'md:pr-12 md:text-right' : 'md:pl-12'}`}>
                  <motion.div
                    className="bg-[#0f172a]/50 backdrop-blur-sm rounded-xl p-6 border border-[#1e293b] shadow-lg"
                    whileHover={{ y: -5, boxShadow: '0 10px 25px -5px rgba(0, 255, 158, 0.1)' }}
                    transition={{ duration: 0.2 }}
                  >
                    <h3 className="text-xl font-bold text-white mb-1">{exp.title}</h3>
                    <h4 className="text-[#00ff9e] font-medium mb-3">{exp.company}</h4>
                    
                    <p className="text-gray-400">{exp.description}</p>
                    
                  </motion.div>
                </div>
              </motion.div>
            );
          })}
        </div>
        
        {/* Remove education section as it's not in the info.json structure */}
      </div>
    </section>
  );
};

export default Experience;