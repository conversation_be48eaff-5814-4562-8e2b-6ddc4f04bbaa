import { useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import info from '@/assets/info.json';

interface SkillsProps {
  appContext: {
    soundEnabled: boolean;
    toggleSound: () => void;
    reducedMotion: boolean;
    toggleReducedMotion: () => void;
    playSoundEffect: (type: 'hover' | 'click' | 'transition') => void;
  };
}

const Skills: React.FC<SkillsProps> = ({ appContext }) => {
  const { reducedMotion, playSoundEffect } = appContext;
  const skillsRef = useRef<HTMLDivElement>(null);
  useInView(skillsRef, { once: true, amount: 0.2 }); // For potential future animations
  
  // Group skills by predefined categories
  const skillsByCategory = {
    'Programming Languages': info.skills.programmingLanguages,
    'Databases': info.skills.databases,
    'Frameworks': info.skills.frameworks,
    'Tools': info.skills.tools
  };
  
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3
      }
    }
  };
  
  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15
      }
    }
  };
  
  return (
    <section id="skills" className="min-h-screen pt-24 pb-16 px-4 relative overflow-hidden">
      <div className="container mx-auto max-w-5xl relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="mb-12 text-center"
        >
          <h2 className="text-4xl md:text-5xl font-bold font-space-grotesk mb-4">
            My <span className="text-[#00ff9e]">Skills</span>
          </h2>
          <div className="w-24 h-1 bg-[#00ff9e] mx-auto rounded-full" />
        </motion.div>
        
        <div ref={skillsRef} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {Object.entries(skillsByCategory).map(([category, skills], categoryIndex) => (
            <motion.div
              key={category}
              className="bg-[#0f172a]/50 backdrop-blur-sm rounded-xl p-6 border border-[#1e293b] shadow-lg"
              variants={reducedMotion ? {} : containerVariants}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              transition={{ delay: categoryIndex * 0.1 }}
            >
              <motion.h3 
                className="text-xl font-bold mb-4 text-[#00ff9e] font-sora"
                variants={reducedMotion ? {} : itemVariants}
              >
                {category}
              </motion.h3>
              
              <motion.div 
                className="grid grid-cols-2 gap-3"
                variants={reducedMotion ? {} : containerVariants}
              >
                {skills.map((skill) => (
                  <motion.div
                    key={skill.name}
                    className="flex items-center gap-2"
                    variants={reducedMotion ? {} : itemVariants}
                    whileHover={{ scale: 1.05, x: 5 }}
                    onHoverStart={() => playSoundEffect('hover')}
                  >
                    <img 
                      src={skill.logo} 
                      alt={skill.name} 
                      className="w-6 h-6 object-contain"
                    />
                    <span className="text-gray-300">{skill.name}</span>
                  </motion.div>
                ))}
              </motion.div>
            </motion.div>
          ))}
        </div>
        
        {/* Remove proficiency section as it's not in the info.json structure */}
      </div>
    </section>
  );
};

export default Skills;