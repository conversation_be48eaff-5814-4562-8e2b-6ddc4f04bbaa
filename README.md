# WebGL Portfolio Website

A professional portfolio website built with React, Three.js, and WebGL effects. This portfolio features interactive 3D elements, particle backgrounds, custom cursor effects, and smooth animations.

## Features

- Interactive WebGL backgrounds and effects using Three.js
- Custom cursor with magnetic hover effect
- Particle system background with interactive elements
- Sound effects for UI interactions (hover, click, transitions)
- Responsive design for all device sizes
- Accessibility features including reduced motion and sound toggles
- Dynamic content loaded from resume data
- Smooth page transitions and scroll animations
- 3D interactive project cards

## Tech Stack

- **Frontend Framework**: React with TypeScript
- **Build Tool**: Vite
- **Styling**: TailwindCSS
- **3D Graphics**: Three.js
- **Animations**: GSAP and Framer Motion
- **Sound Effects**: Howler.js
- **Routing**: React Router DOM

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn

### Installation

1. Clone the repository
2. Install dependencies:

```bash
npm install
# or
yarn install
```

3. Start the development server:

```bash
npm run dev
# or
yarn dev
```

4. Open your browser and navigate to `http://localhost:5173`

## Build for Production

```bash
npm run build
# or
yarn build
```

## Project Structure

```
/src
  /assets        # Static assets like images, fonts, etc.
  /components    # Reusable UI components
    /ui          # Basic UI components
    /layout      # Layout components
    /3d          # Three.js and WebGL components
  /data          # Data files and parsers
  /hooks         # Custom React hooks
  /pages         # Page components
  /styles        # Global styles and Tailwind config
  /utils         # Utility functions
  App.tsx        # Main application component
  main.tsx       # Entry point
```

## Accessibility Features

This portfolio includes several accessibility features:

- Skip animation button for users who prefer reduced motion
- Sound toggle for users who prefer no sound effects
- Keyboard navigation support
- Semantic HTML structure
- ARIA attributes where appropriate
- Color contrast compliance

## Credits

Design inspiration:
- [Hatom](https://www.hatom.com/)
- [They Call Me Giulio](https://www.theycallmegiulio.com/)

## License

MIT