import { motion } from 'framer-motion';
import { useState } from 'react';

interface AccessibilityControlsProps {
  soundEnabled: boolean;
  toggleSound: () => void;
  reducedMotion: boolean;
  toggleReducedMotion: () => void;
}

const AccessibilityControls: React.FC<AccessibilityControlsProps> = ({
  soundEnabled,
  toggleSound,
  reducedMotion,
  toggleReducedMotion
}) => {
  const [expanded, setExpanded] = useState(false);
  
  const toggleExpanded = () => {
    setExpanded(prev => !prev);
  };
  
  return (
    <div className="fixed bottom-4 right-4 z-50">
      <motion.div 
        className="bg-[#0f172a] rounded-lg shadow-lg overflow-hidden border border-[#1e293b]"
        initial={{ width: '48px', height: '48px' }}
        animate={{
          width: expanded ? '240px' : '48px',
          height: expanded ? '120px' : '48px',
        }}
        transition={{ type: 'spring', stiffness: 300, damping: 25 }}
      >
        {/* Toggle button */}
        <button 
          onClick={toggleExpanded}
          className="w-12 h-12 flex items-center justify-center text-white hover:text-[#00ff9e] transition-colors"
          aria-label="Accessibility options"
          data-magnetic
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <path d="M12 16v-4"></path>
            <path d="M12 8h.01"></path>
          </svg>
        </button>
        
        {/* Controls */}
        {expanded && (
          <div className="p-4 pt-0">
            <h3 className="text-white text-sm font-medium mb-2">Accessibility</h3>
            
            <div className="flex flex-col gap-2">
              {/* Sound toggle */}
              <div className="flex items-center justify-between">
                <label htmlFor="sound-toggle" className="text-gray-300 text-xs">
                  Sound Effects
                </label>
                <button
                  id="sound-toggle"
                  onClick={toggleSound}
                  className={`w-10 h-5 rounded-full relative ${soundEnabled ? 'bg-[#00ff9e]' : 'bg-gray-600'} transition-colors`}
                  aria-pressed={soundEnabled}
                  aria-label="Toggle sound effects"
                >
                  <span 
                    className={`absolute top-0.5 left-0.5 w-4 h-4 rounded-full bg-white transform transition-transform ${soundEnabled ? 'translate-x-5' : ''}`}
                  />
                </button>
              </div>
              
              {/* Reduced motion toggle */}
              <div className="flex items-center justify-between">
                <label htmlFor="motion-toggle" className="text-gray-300 text-xs">
                  Reduced Motion
                </label>
                <button
                  id="motion-toggle"
                  onClick={toggleReducedMotion}
                  className={`w-10 h-5 rounded-full relative ${reducedMotion ? 'bg-[#00ff9e]' : 'bg-gray-600'} transition-colors`}
                  aria-pressed={reducedMotion}
                  aria-label="Toggle reduced motion"
                >
                  <span 
                    className={`absolute top-0.5 left-0.5 w-4 h-4 rounded-full bg-white transform transition-transform ${reducedMotion ? 'translate-x-5' : ''}`}
                  />
                </button>
              </div>
            </div>
          </div>
        )}
      </motion.div>
      
      {/* Skip animation button - always visible for accessibility */}
      <button
        className="skip-animation-button absolute left-4 top-4 bg-[#0f172a] text-white px-4 py-2 rounded opacity-0 focus:opacity-100 transition-opacity border border-[#1e293b]"
        onClick={() => {
          if (!reducedMotion) toggleReducedMotion();
          // Focus on main content
          document.querySelector('main')?.focus();
        }}
      >
        Skip Animations
      </button>
    </div>
  );
};

export default AccessibilityControls;