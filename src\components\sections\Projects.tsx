import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import info from '@/assets/info.json';

interface ProjectsProps {
  appContext: {
    soundEnabled: boolean;
    toggleSound: () => void;
    reducedMotion: boolean;
    toggleReducedMotion: () => void;
    playSoundEffect: (type: 'hover' | 'click' | 'transition') => void;
  };
}

const Projects: React.FC<ProjectsProps> = ({ appContext }) => {
  const { reducedMotion, playSoundEffect } = appContext;
  const [activeFilter, setActiveFilter] = useState<string>('all');
  const [selectedProject, setSelectedProject] = useState<number | null>(null);
  
  // Extract unique positions from projects
  const categories = ['all', ...new Set(info.projects.map(project => project.position))];
  
  // Filter projects based on active filter
  const filteredProjects = activeFilter === 'all' 
    ? info.projects 
    : info.projects.filter(project => project.position === activeFilter);
  
  // Handle filter change
  const handleFilterChange = (category: string) => {
    playSoundEffect('click');
    setActiveFilter(category);
  };
  
  // Handle project selection
  const handleProjectClick = (index: number) => {
    playSoundEffect('click');
    setSelectedProject(index);
  };
  
  // Handle project modal close
  const handleCloseModal = () => {
    playSoundEffect('click');
    setSelectedProject(null);
  };
  
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3
      }
    }
  };
  
  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15
      }
    }
  };
  
  return (
    <div className="min-h-screen pt-24 pb-16 px-4 relative overflow-hidden">
      <div className="container mx-auto max-w-6xl relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="mb-12 text-center"
        >
          <h2 className="text-4xl md:text-5xl font-bold font-space-grotesk mb-4">
            My <span className="text-[#00ff9e]">Projects</span>
          </h2>
          <div className="w-24 h-1 bg-[#00ff9e] mx-auto rounded-full" />
        </motion.div>
        
        {/* Filter buttons */}
        <motion.div 
          className="flex flex-wrap justify-center gap-3 mb-10"
          initial={{ opacity: 0, y: 10 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          {categories.map((category) => (
            <motion.button
              key={category}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${activeFilter === category ? 'bg-[#00ff9e] text-[#0a0f1d]' : 'bg-[#0f172a]/50 text-gray-300 hover:bg-[#00ff9e]/20 hover:text-[#00ff9e]'}`}
              onClick={() => handleFilterChange(category)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onHoverStart={() => playSoundEffect('hover')}
              data-magnetic
            >
              {category.charAt(0).toUpperCase() + category.slice(1)}
            </motion.button>
          ))}
        </motion.div>
        
        {/* Projects grid */}
        <motion.div 
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          variants={reducedMotion ? {} : containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {filteredProjects.map((project, index) => (
            <motion.div
              key={`${project.title}-${index}`}
              className="bg-[#0f172a]/50 backdrop-blur-sm rounded-xl overflow-hidden border border-[#1e293b] shadow-lg"
              variants={reducedMotion ? {} : itemVariants}
              whileHover={{ y: -10, boxShadow: '0 15px 30px -10px rgba(0, 255, 158, 0.2)' }}
              transition={{ duration: 0.3 }}
              onClick={() => handleProjectClick(index)}
            >
              {/* Project image/thumbnail */}
              <div className="h-48 bg-gradient-to-br from-[#0a0f1d] to-[#1e293b] relative overflow-hidden">
                {project.image ? (
                  <img 
                    src={project.image} 
                    alt={project.title} 
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <span className="text-4xl text-[#00ff9e]">{project.title.charAt(0)}</span>
                  </div>
                )}
                
                {/* Position badge */}
                <div className="absolute top-3 right-3 px-2 py-1 rounded-full bg-[#00ff9e]/20 border border-[#00ff9e]/30 text-[#00ff9e] text-xs font-medium">
                  {project.position}
                </div>
              </div>
              
              {/* Project info */}
              <div className="p-5">
                <h3 className="text-xl font-bold text-white mb-2">{project.title}</h3>
                <div className="text-gray-400 text-sm mb-4">
                  <p className="mb-2">{project.description}</p>
                  <ul className="list-disc list-inside space-y-1">
                    {project.responsibilities.map((resp, idx) => (
                      <li key={idx} className="text-sm text-gray-400">{resp}</li>
                    ))}
                  </ul>
                </div>
                
                {/* Technologies */}
                <div className="flex flex-wrap gap-2">
                  {project.technologies.map((tech, idx) => (
                    <span 
                      key={idx} 
                      className="text-xs px-2 py-1 rounded-full bg-[#0a0f1d] text-[#00ff9e] border border-[#00ff9e]/20"
                    >
                      {tech}
                    </span>
                  ))}
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>
        
        {/* No projects message */}
        {filteredProjects.length === 0 && (
          <motion.div 
            className="text-center py-16"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <p className="text-gray-400 text-lg">No projects found in this category.</p>
          </motion.div>
        )}
        
        {/* Project detail modal */}
        <AnimatePresence>
          {selectedProject !== null && (
            <motion.div
              className="fixed inset-0 bg-[#0a0f1d]/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={handleCloseModal}
            >
              <motion.div
                className="bg-[#0f172a] rounded-xl w-full max-w-3xl max-h-[90vh] overflow-y-auto shadow-2xl"
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                transition={{ type: 'spring', damping: 20 }}
                onClick={(e) => e.stopPropagation()}
              >
                {/* Project image */}
                <div className="h-64 md:h-80 bg-gradient-to-br from-[#0a0f1d] to-[#1e293b] relative">
                  {filteredProjects[selectedProject].image ? (
                    <img 
                      src={filteredProjects[selectedProject].image} 
                      alt={filteredProjects[selectedProject].title} 
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <span className="text-6xl text-[#00ff9e]">{filteredProjects[selectedProject].title.charAt(0)}</span>
                    </div>
                  )}
                  
                  {/* Close button */}
                  <button 
                    className="absolute top-4 right-4 w-10 h-10 rounded-full bg-[#0a0f1d]/70 backdrop-blur-sm flex items-center justify-center text-white hover:bg-[#00ff9e] hover:text-[#0a0f1d] transition-colors duration-300"
                    onClick={handleCloseModal}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <line x1="18" y1="6" x2="6" y2="18"></line>
                      <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                  </button>
                </div>
                
                {/* Project details */}
                <div className="p-6">
                  {/* <div className="flex items-center justify-between mb-4">
                    <h3 className="text-2xl font-bold text-white">{filteredProjects[selectedProject].title}</h3>
                    <span className="px-3 py-1 rounded-full bg-[#00ff9e]/20 border border-[#00ff9e]/30 text-[#00ff9e] text-sm font-medium">
                      {filteredProjects[selectedProject].category}
                    </span>
                  </div> */}
                  
                  <p className="text-gray-300 mb-6">{filteredProjects[selectedProject].description}</p>
                  
                  {/* Features */}
                  {/* {filteredProjects[selectedProject].features && (
                    <div className="mb-6">
                      <h4 className="text-lg font-bold text-[#00ff9e] mb-3">Key Features</h4>
                      <ul className="space-y-2">
                        {filteredProjects[selectedProject].features.map((feature, idx) => (
                          <li key={idx} className="flex items-start text-gray-400">
                            <span className="inline-block w-2 h-2 bg-[#00ff9e] rounded-full mt-2 mr-2" />
                            <span>{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )} */}
                  
                  {/* Technologies */}
                  {filteredProjects[selectedProject].technologies && (
                    <div className="mb-6">
                      <h4 className="text-lg font-bold text-[#00ff9e] mb-3">Technologies</h4>
                      <div className="flex flex-wrap gap-2">
                        {filteredProjects[selectedProject].technologies.map((tech, idx) => (
                          <span 
                            key={idx} 
                            className="text-sm px-3 py-1 rounded-full bg-[#0a0f1d] text-[#00ff9e] border border-[#00ff9e]/20"
                          >
                            {tech}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {/* Links */}
                  {/* <div className="flex flex-wrap gap-4 mt-8">
                    {filteredProjects[selectedProject].liveUrl && (
                      <a 
                        href={filteredProjects[selectedProject].liveUrl} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="px-6 py-2 rounded-full bg-[#00ff9e] text-[#0a0f1d] font-medium hover:bg-[#00ff9e]/90 transition-colors duration-300 flex items-center gap-2"
                        onClick={(e) => e.stopPropagation()}
                        data-magnetic
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                          <polyline points="15 3 21 3 21 9"></polyline>
                          <line x1="10" y1="14" x2="21" y2="3"></line>
                        </svg>
                        Live Demo
                      </a>
                    )}
                    
                    {filteredProjects[selectedProject].githubUrl && (
                      <a 
                        href={filteredProjects[selectedProject].githubUrl} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="px-6 py-2 rounded-full bg-[#0f172a] text-white border border-[#1e293b] hover:border-[#00ff9e] hover:text-[#00ff9e] transition-colors duration-300 flex items-center gap-2"
                        onClick={(e) => e.stopPropagation()}
                        data-magnetic
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"></path>
                        </svg>
                        View Code
                      </a>
                    )}
                  </div> */}
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default Projects;