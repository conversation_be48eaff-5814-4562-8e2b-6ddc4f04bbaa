@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --color-bg: #0a0f1d;
  --color-accent: #00ff9e;
  --color-text: #ffffff;
  --font-primary: 'Space Grotesk', sans-serif;
  --font-secondary: 'Inter', sans-serif;
  --font-tertiary: 'Sora', sans-serif;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  cursor: none;
}

html,
body {
  width: 100%;
  min-height: 100vh;
  background-color: var(--color-bg);
  color: var(--color-text);
  font-family: var(--font-primary);
  overflow-x: hidden;
}

#root {
  width: 100%;
  min-height: 100vh;
}

.no-cursor {
  cursor: none;
}

.accent-text {
  color: var(--color-accent);
}

.accent-glow {
  text-shadow: 0 0 10px var(--color-accent), 0 0 20px var(--color-accent);
}

.accent-border {
  border-color: var(--color-accent);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--color-bg);
}

::-webkit-scrollbar-thumb {
  background: var(--color-accent);
  border-radius: 3px;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(50px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes glow {
  0% { text-shadow: 0 0 5px var(--color-accent); }
  50% { text-shadow: 0 0 20px var(--color-accent), 0 0 30px var(--color-accent); }
  100% { text-shadow: 0 0 5px var(--color-accent); }
}

/* Skip animation button for accessibility */
.skip-animations {
  position: fixed;
  top: 10px;
  right: 10px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid var(--color-accent);
  color: var(--color-text);
  padding: 8px 12px;
  border-radius: 4px;
  z-index: 1000;
  font-family: var(--font-secondary);
  font-size: 14px;
  cursor: pointer;
}

.skip-animations:hover {
  background: rgba(57, 255, 138, 0.2);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .hide-on-mobile {
    display: none;
  }
}